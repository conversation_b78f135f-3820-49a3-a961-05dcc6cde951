"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"

/**
 * 登录拦截钩子，用于需要登录才能执行的操作
 * @returns 一个包含登录状态和处理函数的对象
 */
export function useAuthGuard() {
  const { isAuthenticated, isLoading } = useAuth()
  const [loginDialogOpen, setLoginDialogOpen] = useState(false)
  const [pendingCallback, setPendingCallback] = useState<(() => void) | null>(null)

  // 当用户登录状态变化时，执行待处理的回调
  useEffect(() => {
    if (isAuthenticated && pendingCallback) {
      // 执行待处理的回调
      pendingCallback()
      // 清除待处理的回调
      setPendingCallback(null)
      // 关闭登录对话框
      setLoginDialogOpen(false)
    }
  }, [isAuthenticated, pendingCallback])

  /**
   * 执行需要登录的操作
   * @param callback 登录后���执行的回调函数
   * @returns 如果已登录，直接执行回调；否则打开登录对话框
   */
  const requireAuth = (callback: () => void) => {
    if (isLoading) {
      return // 加载中，不执行任何操作
    }

    if (isAuthenticated) {
      callback()
    } else {
      // 保存回调函数，等待登录成功后执行
      setPendingCallback(() => callback)
      setLoginDialogOpen(true)
    }
  }

  return {
    isAuthenticated,
    isLoading,
    loginDialogOpen,
    setLoginDialogOpen,
    requireAuth,
  }
}
