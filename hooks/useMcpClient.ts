import { useState, useEffect } from 'react';
import { McpClient } from '@/lib/mcp/client';

export function useMcpClient(baseUrl: string) {
  const [client, setClient] = useState<McpClient | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!baseUrl) return;
    const mcpClient = new McpClient(baseUrl);
    setClient(mcpClient);

    const connect = async () => {
      try {
        await mcpClient.connect();
        setIsConnected(true);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to connect to MCP server'));
        setIsConnected(false);
      }
    };

    connect();

    return () => {
      // Cleanup if needed
    };
  }, [baseUrl]);

  return {
    client,
    isConnected,
    error
  };
}