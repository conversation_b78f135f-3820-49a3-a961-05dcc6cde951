"use client"
import { Sun, Moon } from "@icon-park/react"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"

import { Button } from "@/components/ui/button"

export function ThemeToggle() {
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // 确保组件只在客户端渲染后才显示，避免服务器端渲染不匹配
  useEffect(() => {
    setMounted(true)
  }, [])

  // 直接切换主题
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }

  if (!mounted) {
    return <Button variant="outline" size="icon" className="rounded-full w-9 h-9" />
  }

  return (
    <Button
      variant="outline"
      size="icon"
      className="rounded-full w-9 h-9"
      onClick={toggleTheme}
      aria-label={theme === "dark" ? "切换到亮色模式" : "切换到暗色模式"}
    >
      <Sun theme="outline" size="20" className="rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon
        theme="outline"
        size="20"
        className="absolute rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
      />
    </Button>
  )
}
