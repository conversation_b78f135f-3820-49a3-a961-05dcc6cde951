import { Skeleton } from "@/components/ui/skeleton"

export function HomeSkeleton() {
  return (
    <div className="container mx-auto py-6 px-4">
      {/* Banner 骨架屏 */}
      <div className="mb-8">
        <div className="text-center space-y-4 mb-8">
          <Skeleton className="h-12 w-96 mx-auto" />
          <Skeleton className="h-6 w-64 mx-auto" />
        </div>
        <div className="max-w-2xl mx-auto">
          <Skeleton className="h-12 w-full" />
        </div>
      </div>

      {/* 筛选和排序区域骨架屏 */}
      <div className="mb-6">
        <Skeleton className="h-10 w-32 mb-4" />
      </div>

      <div className="grid gap-6 md:grid-cols-[200px_1fr]">
        {/* 侧边栏骨架屏 */}
        <aside className="hidden md:block space-y-4">
          <Skeleton className="h-8 w-full" />
          <div className="space-y-2">
            {Array.from({ length: 8 }).map((_, i) => (
              <Skeleton key={i} className="h-6 w-full" />
            ))}
          </div>
        </aside>

        {/* 主内容区域骨架屏 */}
        <main>
          {/* 排序和筛选按钮骨架屏 */}
          <div className="flex justify-between items-center mb-4">
            <Skeleton className="h-10 w-48" />
            <Skeleton className="h-10 w-32" />
          </div>

          {/* 服务器卡片网格骨架屏 */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 12 }).map((_, i) => (
              <div key={i} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center space-x-3">
                  <Skeleton className="h-12 w-12 rounded" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
                <Skeleton className="h-16 w-full" />
                <div className="flex justify-between items-center">
                  <div className="flex space-x-2">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                  <Skeleton className="h-6 w-12" />
                </div>
              </div>
            ))}
          </div>

          {/* 分页骨架屏 */}
          <div className="flex justify-center mt-8">
            <Skeleton className="h-10 w-64" />
          </div>
        </main>
      </div>
    </div>
  )
} 