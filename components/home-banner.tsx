"use client"

import type React from "react"

import { Input } from "@/components/ui/input"
import { Search } from "@icon-park/react"
import { serverData } from "@/lib/data"
import type { RefObject } from "react"

type HomeBannerProps = {
  searchQuery: string
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  inputRef: RefObject<HTMLInputElement>
  isComposing: RefObject<boolean>
  handleSearch: (value: string) => void
}

export function HomeBanner({ searchQuery, handleInputChange, inputRef, isComposing, handleSearch }: HomeBannerProps) {
  return (
    <div className="w-full py-12 md:py-16 border-b relative overflow-hidden bg-gradient-to-br from-primary/5 via-secondary/5 to-primary/10">
      {/* 装饰性背景元素 */}
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-10 left-1/4 w-64 h-64 rounded-full bg-primary/5 blur-3xl"></div>
        <div className="absolute bottom-10 right-1/4 w-72 h-72 rounded-full bg-secondary/5 blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-accent/5 blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 text-center relative">
        {/*<div className="inline-flex items-center justify-center bg-primary/10 text-primary rounded-full px-4 py-1 mb-6">
          <span className="font-semibold">
            <span className="bg-primary text-primary-foreground px-3 py-0.5 rounded-full mr-1">
              {serverData.length}
            </span>
            MCP 服务
          </span>
        </div>*/}

        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
          精选适合中国用户的 <span className="text-primary">MCP</span>
        </h1>

        <p className="text-muted-foreground max-w-3xl mx-auto mb-8 text-base md:text-lg">
          致力于构建中国MCP开源社区生态，让您的AI助手轻松使用国内服务
        </p>

        {/* 大型搜索框 */}
        <div className="max-w-2xl mx-auto relative mb-6">
          <Search
            theme="outline"
            size="20"
            className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground"
          />
          <Input
            ref={inputRef}
            type="text"
            placeholder="搜索MCP服务器..."
            className="pl-12 py-6 text-lg rounded-lg shadow-md"
            value={searchQuery}
            onChange={handleInputChange}
            onCompositionStart={() => {
              isComposing.current = true
            }}
            onCompositionEnd={(e) => {
              isComposing.current = false
              handleSearch(e.currentTarget.value)
            }}
          />
        </div>
      </div>
    </div>
  )
}
