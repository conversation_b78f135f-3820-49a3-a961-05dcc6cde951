import Link from "next/link"
import { Info, Github, <PERSON>bo, Wecha<PERSON> } from "@icon-park/react"

export function SiteFooter() {
  return (
    <footer className="w-full border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 py-6">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex items-center gap-2">
            <p className="text-sm text-muted-foreground">© 2025 MCP中国. 保留所有权利.</p>
          </div>

          <div className="flex items-center gap-6">
            {/* <Link
              href="/about"
              className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <Info theme="outline" size="16" />
              <span>关于我们</span>
            </Link> */}

            <Link
              href="https://github.com/mcpchina"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <Github theme="outline" size="16" />
              <span>GitHub</span>
            </Link>

            <Link
              href="https://www.weibo.com/u/1653142972  "
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <Weibo theme="outline" size="16" />
              <span>微博</span>
            </Link>

            {/* <Link
              href="/wechat"
              className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <Wechat theme="outline" size="16" />
              <span>公众号</span>
            </Link> */}
          </div>
        </div>

        <div className="mt-6 text-center">
          <p className="text-xs text-muted-foreground">
            MCP (Model Context Protocol) 是一种标准化的协议，用于连接AI模型与外部工具和服务
          </p>
          <p className="text-xs text-muted-foreground">
            <a href="https://beian.miit.gov.cn/" target="_blank">京ICP备2025125215号-1</a >
          </p>
        </div>
      </div>
    </footer>
  )
}
