"use client"

import type React from "react"

import { Computer, Earth } from "@icon-park/react"

type CallMethodFilterProps = {
  callMethodFilters: string[]
  setCallMethodFilters: (filters: string[]) => void
  setCurrentPage: (page: number) => void
}

export function CallMethodFilter({ callMethodFilters, setCallMethodFilters, setCurrentPage }: CallMethodFilterProps) {
  return (
    <div className="flex items-center gap-3">
      <span className="text-sm text-muted-foreground whitespace-nowrap">使用方式：</span>
      <div className="flex gap-2">
        <button
          className={`inline-flex items-center rounded-full px-3 py-1 text-sm transition-colors ${
            callMethodFilters.includes("local")
              ? "bg-primary/15 text-primary border"
              : "bg-muted hover:bg-muted/80 border"
          }`}
          onClick={() => {
            const newFilters = callMethodFilters.includes("local")
              ? callMethodFilters.filter((f) => f !== "local")
              : [...callMethodFilters, "local"]
            setCallMethodFilters(newFilters)
            setCurrentPage(1)
          }}
        >
          <Computer
            theme="outline"
            size="14"
            className={`mr-1 ${callMethodFilters.includes("local") ? "text-primary" : ""}`}
          />
          本地安装
        </button>

        <button
          className={`inline-flex items-center rounded-full px-3 py-1 text-sm transition-colors ${
            callMethodFilters.includes("online")
              ? "bg-primary/15 text-primary border"
              : "bg-muted hover:bg-muted/80 border"
          }`}
          onClick={() => {
            const newFilters = callMethodFilters.includes("online")
              ? callMethodFilters.filter((f) => f !== "online")
              : [...callMethodFilters, "online"]
            setCallMethodFilters(newFilters)
            setCurrentPage(1)
          }}
        >
          <Earth
            theme="outline"
            size="14"
            className={`mr-1 ${callMethodFilters.includes("online") ? "text-primary" : ""}`}
          />
          在线调用
        </button>
      </div>
    </div>
  )
}
