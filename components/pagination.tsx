"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Left, Right, DoubleLeft, DoubleRight } from "@icon-park/react"

type PaginationProps = {
  currentPage: number
  totalPages: number
  goToPage: (page: number) => void
  isMobile: boolean
}

export function Pagination({ currentPage, totalPages, goToPage, isMobile }: PaginationProps) {
  // 生成页码数组
  const getPageNumbers = () => {
    const pageNumbers = []
    const maxVisiblePages = isMobile ? 3 : 5

    if (totalPages <= maxVisiblePages) {
      // 如果总页数小于等于最大可见页数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i)
      }
    } else {
      // 否则，显示当前页附近的页码
      let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))
      let endPage = startPage + maxVisiblePages - 1

      if (endPage > totalPages) {
        endPage = totalPages
        startPage = Math.max(1, endPage - maxVisiblePages + 1)
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i)
      }
    }

    return pageNumbers
  }

  if (totalPages <= 1) return null

  return (
    <div className="flex justify-center mt-6">
      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size="icon"
          onClick={() => goToPage(1)}
          disabled={currentPage === 1}
          className="hidden sm:flex hover-orange w-8 h-8"
        >
          <DoubleLeft theme="outline" size="16" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => goToPage(currentPage - 1)}
          disabled={currentPage === 1}
          className="hover-orange w-8 h-8"
        >
          <Left theme="outline" size="16" />
        </Button>

        {getPageNumbers().map((page) => (
          <Button
            key={page}
            variant={currentPage === page ? "default" : "outline"}
            size="icon"
            onClick={() => goToPage(page)}
            className={`w-8 h-8 ${currentPage === page ? "bg-primary text-primary-foreground" : "hover-orange"}`}
          >
            {page}
          </Button>
        ))}

        <Button
          variant="outline"
          size="icon"
          onClick={() => goToPage(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="hover-orange w-8 h-8"
        >
          <Right theme="outline" size="16" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => goToPage(totalPages)}
          disabled={currentPage === totalPages}
          className="hidden sm:flex hover-orange w-8 h-8"
        >
          <DoubleRight theme="outline" size="16" />
        </Button>
      </div>
    </div>
  )
}
