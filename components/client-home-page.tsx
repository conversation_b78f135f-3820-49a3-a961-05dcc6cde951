"use client"

import type React from "react"
import { useState, useEffect, useRef, useCallback } from "react"
import { Sheet, <PERSON>et<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Filter, Fire, UpdateRotation } from "@icon-park/react"
import { useMediaQuery } from "@/hooks/use-media-query"
import { useSearchParams } from "next/navigation"
import { ServerCard } from "@/components/server-card"
import { FilterSidebar } from "@/components/filter-sidebar"
import { Pagination } from "@/components/pagination"
import { HomeBanner } from "@/components/home-banner"
import { CallMethodFilter } from "@/components/call-method-filter"
import { EmptyState } from "@/components/empty-state"
import projectsApi from "@/lib/api/apis/projects"
import mapApi from "@/lib/api/apis/map"

// 排序类型
type SortType = string

// 每页显示的服务器数量
const ITEMS_PER_PAGE = 12

interface ClientHomePageProps {
  initialProjects: any[]
  initialTotalCount: number
  initialCategories: any[]
  totalProjects: number
}

export function ClientHomePage({ 
  initialProjects, 
  initialTotalCount, 
  initialCategories, 
  totalProjects 
}: ClientHomePageProps) {
  // 使用useState代替useRef来跟踪是否正在清除筛选条件
  const [allCategories, setAllCategories] = useState<any[]>([])
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState(sessionStorage?.getItem('searchQuery') || "")
  const [selectedCategory, setSelectedCategory] = useState(sessionStorage?.getItem('selectedCategory')? JSON.parse(sessionStorage.getItem('selectedCategory') as string) : {
    value: "all",
    label: "全部",
    projectsCount: 0
  })
  const [totalPages, setTotalPages] = useState(Math.ceil(initialTotalCount / ITEMS_PER_PAGE))
  const [filteredServers, setFilteredServers] = useState<any[]>([])
  const [sortType, setSortType] = useState<SortType>(sessionStorage?.getItem('sortType') || "latest")
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(Number(sessionStorage?.getItem('currentPage')) || 1)
  const [callMethodFilters, setCallMethodFilters] = useState<string[]>(sessionStorage?.getItem('callMethodFilters') ? JSON.parse(sessionStorage.getItem('callMethodFilters') as string) : [])
  const isComposing = useRef(false)
  const inputRef = useRef<HTMLInputElement>(null!)
  const hasFocus = useRef(false)

  // 检测是否为移动设备
  const isMobile = useMediaQuery("(max-width: 768px)")
  // 初始化分类数据
  useEffect(() => {
    const all = {
      value: "all",
      label: "全部",
      projectsCount: totalProjects
    }
    const allCategoriesData = [all, ...initialCategories]
    setAllCategories(allCategoriesData)
    // setSelectedCategory(all)
  }, [initialCategories, totalProjects])

  // 获取项目列表数据
  const fetchProjects = useCallback(async () => {
    try {
      const params = {
        page: currentPage,
        pageSize: ITEMS_PER_PAGE,
        category: selectedCategory.value !== "all" ? selectedCategory.value : undefined,
        keyword: searchQuery || undefined,
        orderBy: sortType,
        callMethod: callMethodFilters.length === 2 || callMethodFilters.length === 0 ? "" : callMethodFilters[0]
      }
      
      const response: any = await projectsApi.getProjectsList(params)
      if (response && response.data) {
        setFilteredServers(response.data.list || [])  
        setTotalPages(Math.ceil(response.data.total / ITEMS_PER_PAGE));
      }
    } catch (error) {
      console.error("获取项目列表失败:", error)
    }
  }, [currentPage, selectedCategory, searchQuery, sortType, callMethodFilters])

  // 处理URL参数中的授权回调
  useEffect(() => {
    if(searchParams.get("code") && searchParams.get("state")) {
      let code = searchParams.get("code")
      let state = searchParams.get("state")
      console.log("code", code)
      console.log("state", state)
      let params = {
        key: state,
        value: code
      }
      mapApi.setString(params)
    }
  }, [searchParams])
  useEffect(() => {
    sessionStorage.removeItem('searchQuery')
    sessionStorage.removeItem('selectedCategory')
    sessionStorage.removeItem('sortType')
    sessionStorage.removeItem('currentPage')
    sessionStorage.removeItem('callMethodFilters')
  }, [])
  const sessionCatchData = () => {
    sessionStorage.setItem('searchQuery', searchQuery)
    sessionStorage.setItem('selectedCategory', JSON.stringify(selectedCategory))
    sessionStorage.setItem('sortType', sortType)
    sessionStorage.setItem('currentPage', currentPage.toString())
    sessionStorage.setItem('callMethodFilters', JSON.stringify(callMethodFilters))
  }
  // --- 删除原有依赖 fetchProjects 的 useEffect ---
  // useEffect(() => {
  //   // 如果不是初始加载，则重新获取数据
  //   if (allCategories.length > 0) {
  //     fetchProjects()
  //   }
  // }, [fetchProjects, allCategories])

  // --- 新增防抖 useEffect ---
  useEffect(() => {
    if (allCategories.length === 0) return
    const handler = setTimeout(() => {
      fetchProjects()
    }, 200)
    return () => clearTimeout(handler)
  }, [searchQuery, currentPage, selectedCategory, sortType, callMethodFilters, allCategories, fetchProjects])
  

  // 处理搜索
  const handleSearch = useCallback((value: string) => {
    console.log("搜索:", value)
  }, [])

  // 处理输入变化
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      // 记录当前焦点状态
      hasFocus.current = document.activeElement === inputRef.current

      const value = e.target.value
      setSearchQuery(value)
      setCurrentPage(1)
      // 只有在不是中文输入法编辑状态时才触发搜索
      if (!isComposing.current) {
        handleSearch(value)
      }
    },
    [handleSearch],
  )

  // 修改clearFilters函数
  const clearFilters = () => {
    // 清除状态
    setCurrentPage(1)
    setSearchQuery("")
    setSelectedCategory(allCategories[0])
    setSortType("latest")
    setCallMethodFilters([])
    // 如果URL中有参数，使用window.location.href更新URL
    if (searchParams.has("tag")) {
      window.location.href = "/"
    }

    if (isMobile) {
      setIsDrawerOpen(false)
    }
  }

  // 页码导航
  const goToPage = (page: number) => {
    let pageNum: number = Math.max(1, Math.min(page, totalPages))
    setCurrentPage(pageNum)
  }

  return (
    <>
      <style jsx global>{`
        .custom-tooltip {
          visibility: hidden;
          opacity: 0;
          transition: opacity 0.2s, visibility 0.2s;
          transition-delay: 0s;
        }
        
        .tooltip-trigger:hover .custom-tooltip {
          visibility: visible;
          opacity: 1;
          transition-delay: 0.5s; /* 500ms delay before showing tooltip */
        }
      `}</style>
      
      {/* Banner Section */}
      <HomeBanner
        searchQuery={searchQuery}
        handleInputChange={handleInputChange}
        inputRef={inputRef}
        isComposing={isComposing}
        handleSearch={handleSearch}
      />

      <div className="container mx-auto py-6 px-4">
        <header>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* 移动设备上的过滤器按钮 */}
              <Sheet open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon" className="md:hidden hover-orange">
                    <Filter theme="outline" size="20" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-[280px] sm:w-[350px]">
                  <h2 className="text-xl font-bold mb-4">筛选选项</h2>
                  <FilterSidebar
                    selectedCategory={selectedCategory}
                    setSelectedCategory={setSelectedCategory}
                    clearFilters={clearFilters}
                    categories={allCategories}
                    setCurrentPage={setCurrentPage}
                  />
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </header>

        <div className="grid gap-6 md:grid-cols-[200px_1fr]">
          {/* 桌面端侧边栏 - 在移动设备上隐藏 */}
          <aside className="hidden md:block">
            <FilterSidebar
              selectedCategory={selectedCategory}
              setSelectedCategory={setSelectedCategory}
              clearFilters={clearFilters}
              categories={allCategories}
              setCurrentPage={setCurrentPage}
            />
          </aside>

          {/* 带有服务器列表的主要内容 */}
          <main>
            <div className="flex flex-col sm:flex-row justify-between items-center sm:items-center gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Tabs
                  value={sortType}
                  onValueChange={(value) => {
                    setSortType(value as SortType)
                    setCurrentPage(1)
                  }}
                  className="w-full"
                >
                  <TabsList className="w-full bg-muted border border-border p-1">
                    <TabsTrigger
                      value="latest"
                      className="flex-1 flex items-center justify-center gap-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                    >
                      <UpdateRotation theme="outline" size="16" />
                      <span>最新</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="hot"
                      className="flex-1 flex items-center justify-center gap-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                    >
                      <Fire theme="outline" size="16" />
                      <span>最热</span>
                    </TabsTrigger>
                    
                  </TabsList>
                </Tabs>
              </div>

              <CallMethodFilter callMethodFilters={callMethodFilters} setCallMethodFilters={setCallMethodFilters} setCurrentPage={setCurrentPage} />
            </div>

            {filteredServers.length > 0 ? (
              <>
                <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                  {filteredServers.map((server: any) => (
                    <ServerCard key={server.ID} server={server} sessionCatchData={sessionCatchData} />
                  ))}
                </div>

                {/* 分页控件 */}
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  goToPage={goToPage}
                  isMobile={isMobile}
                />
              </>
            ) : (
              <EmptyState />
            )}
          </main>
        </div>
      </div>
    </>
  )
} 