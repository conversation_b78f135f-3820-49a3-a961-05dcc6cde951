"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { User, Lock, Github, Wechat, TencentQq } from "@icon-park/react"
import { useAuth } from "@/contexts/auth-context"
import userApi from "@/lib/api/apis/user"
import baseApi from "@/lib/api/apis/base"
import { toast } from 'sonner';
import { Modal } from 'antd';
import wechatApi from "@/lib/api/apis/wechat"
import './login-dialog.css'
interface LoginDialogProps {
  isMobile?: boolean
  preventScroll?: boolean
  captchaIns?: any
  setCaptchaIns?: (captchaIns: any) => void
}

export function LoginDialog() {
  const { login, isAuthenticated, loginDialogOpen, setLoginDialogOpen } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  // 登录表单状态
  const [loginForm, setLoginForm] = useState({
    account: "",
    password: "",
  })
  const [loginErrors, setLoginErrors] = useState({
    account: "",
    password: "",
  })
  const loginFormRef = useRef(loginForm);
  useEffect(() => {
    loginFormRef.current = loginForm;
  }, [loginForm]);
  useEffect(() => {
    if (isAuthenticated && loginDialogOpen && setLoginDialogOpen) {
      setLoginDialogOpen(false)
    }
  }, [isAuthenticated, loginDialogOpen, setLoginDialogOpen])
  const captchaInsRef = useRef<any>(null)
  const validateCallbackRef = useRef<any>(null)
  // 易盾初始化
  
  const [loginType, setLoginType] = useState<'password' | 'sms'>('password')
  const loginTypeRef = useRef<'password' | 'sms'>(loginType)
  useEffect(() => {
    loginTypeRef.current = loginType
  }, [loginType])
  // 验证码登录相关状态
  const [smsCode, setSmsCode] = useState("")
  const smsCodeRef = useRef("")
  useEffect(() => {
    smsCodeRef.current = smsCode
  }, [smsCode])
  const [smsError, setSmsError] = useState("")
  const [smsCountdown, setSmsCountdown] = useState(0)
  const [isSendingSms, setIsSendingSms] = useState(false)
  const [bindForm, setBindForm] = useState({
    phone: '',
    code: '',
  });

  const [bindErrors, setBindErrors] = useState({
    phone: '',
    code: '',
  });
  const [bindCountdown, setBindCountdown] = useState(0);
  const [isBindSending, setIsBindSending] = useState(false);
  const [bindserData, setBindserData] = useState<any>({});
  const [bindLoading, setBindLoading] = useState(false);

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [showForgotConfirmPassword, setShowForgotConfirmPassword] = useState(false);
  // 验证码倒计时
  useEffect(() => {
    if (smsCountdown > 0) {
      const timer = setTimeout(() => setSmsCountdown(smsCountdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [smsCountdown])

  const getWXCode = () => {
    wechatApi.wechatConfig().then((res: any) => {
      if(res.code === 0) {
        var obj = new (window as any).WxLogin({
          self_redirect: true,
          id:"wxCode", 
          appid: res.data.appId, 
          scope: res.data.scope, 
          redirect_uri: encodeURIComponent(`${window.location.origin}/redirect`),
          stylelite: res.data.stylelite,
          state: res.data.state,
          style: res.data.style,
          onQRcodeReady(){
          }
        });
      }
    })
  }
  const initYiDun = () => {
    const loadCaptcha = () => {
      const initNECaptcha = (window as any).initNECaptcha;
      initNECaptcha({
        captchaId: 'a9efe3dc7e974433841a50b5b747ed70',
        element: '#captcha',
        mode: 'popup',
        width: '320px',
        apiVersion: 2,
        popupStyles: {
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 999999,
          capBarTextAlign: 'center',
          capBarBorderColor: '#fff',
          radius: 8,
          capBarHeight: 50,
          capPaddingTop: 0,
          paddingBottom: 9,
          capBarTextWeight: 500,
          capBarTextColor: '#333'
        },
        onVerify: (err: any, data: any) => {
          if (err) return
          setIsLoading(true)
          if(validateCallbackRef.current) {
            validateCallbackRef.current(data.validate)
            validateCallbackRef.current = null
          }
          captchaInsRef.current && captchaInsRef.current.refresh()
        }
      }, (instance: any) => {
        captchaInsRef.current = instance
      }, (err: any) => {
        // 初始化失败
      })
    };
    const script = document.createElement('script');
    script.src = 'https://cstaticdun.126.net/load.min.js';
    script.onload = loadCaptcha;
    document.body.appendChild(script);
  }
  const initWX = () => {
    const script = document.createElement('script');
    script.src = 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js';
    script.onload = () => {
      console.log('wxLogin.js loaded')
    }
    document.body.appendChild(script);
  }
  const handleWxLogin = (code: string, state: string) => {
    baseApi.unifiedLogin({
      provider: 'wechat',
      code: code,
      metadata: state
    }).then((res: any) => {
      const { action, ...rest } = res.data;
      if (action === 'login') {
        login({
          ...rest.user,
          token: rest.token,
        });
        if (setLoginDialogOpen) setLoginDialogOpen(false);
      } else if (action === 'bind') {
        setBindserData(rest.tempData);
        setLoginMode('bindPhone');
      }
    })
  }
  useEffect(() => {
    initYiDun()
    initWX();
    window.addEventListener('message',  (event) => {
      if(event.data.wxCode && event.data.wxState) {
        handleWxLogin(event.data.wxCode, event.data.wxState)
      }
    })
  }, [])
  useEffect(() => {
    if(loginDialogOpen) {
      getWXCode();
    }
  }, [loginDialogOpen])
  
  // 发送验证码
  const handleSendSmsCode = async () => {
    if (smsCountdown > 0) return
    if (!/^1[3-9]\d{9}$/.test(loginForm.account)) {
      setLoginErrors(prev => ({ ...prev, account: "请输入正确的手机号" }))
      return
    }
    setIsSendingSms(true)
    try {
      await baseApi.sendSMSCode({
        smsType: 2,
        phone: loginForm.account
      })
      setSmsCountdown(60)
      setSmsError("")
      toast.success("验证码已发送")
    } catch {
      setSmsError("验证码发送失败")
    } finally {
      setIsSendingSms(false)
    }
  }

  // 验证码登录校验
  const validateSmsLoginForm = () => {
    let isValid = true
    const newErrors = { account: "", password: "" }
    if (!loginForm.account) {
      newErrors.account = "请输入手机号"
      isValid = false
    } else if (!/^1[3-9]\d{9}$/.test(loginForm.account)) {
      newErrors.account = "请输入正确的手机号"
      isValid = false
    }
    if (!smsCode) {
      setSmsError("请输入验证码")
      isValid = false
    } else {
      setSmsError("")
    }
    setLoginErrors(newErrors)
    return isValid
  }

  const handleLoginInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setLoginForm(prev => ({
      ...prev,
      [name]: value
    }))
  }
  const validateLoginForm = () => {
    const newErrors = {
      account: "",
      password: "",
    }
    let isValid = true
    if (!loginForm.account) {
      newErrors.account = "请输入手机号"
      isValid = false
    }
    if (!loginForm.password) {
      newErrors.password = "请输入密码"
      isValid = false
    }
    setLoginErrors(newErrors)
    return isValid
  }
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (loginType === 'password' && !validateLoginForm() || loginType === 'sms' && !validateSmsLoginForm()) {
      return
    }
    validateCallbackRef.current = loginFn;
    captchaInsRef.current && captchaInsRef.current.verify()
  }

  // 易盾验证通过后实际登录
  const loginFn = (validate: string) => {
    setIsLoading(true)
    let params: any = {
      provider: loginTypeRef.current === 'password' ? "username" : "phone",
      validate: validate
    }
    if (loginTypeRef.current === 'password') {
      params.username = loginFormRef.current.account
      params.password = loginFormRef.current.password
    } else if (loginTypeRef.current === 'sms') {
      params.provider = "phone"
      params.phone = loginFormRef.current.account
      params.code = smsCodeRef.current
    }
    baseApi.unifiedLogin(params).then((res: any) => {
      login({
        ...res.data.user,
        token: res.data.token
      })
    }).catch((error: any) => {
      // 可以加toast提示
    }).finally(() => {
      setIsLoading(false)
    })
  }
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setLoginForm({
        account: "",
        password: "",
      });
      setLoginErrors({
        account: "",
        password: "",
      });
      setIsLoading(false);
      setLoginType('password');
      setLoginMode('login');
      setSmsCode('');
      setSmsError('');
      setRegisterForm({ account: '', password: '', confirmPassword: '', smsCode: '' });
      setRegisterErrors({ account: '', password: '', confirmPassword: '', smsCode: '' });
      setRegisterCountdown(0);
      setIsRegisterSending(false);
      setForgotForm({ account: '', smsCode: '', password: '', confirmPassword: '' });
      setForgotErrors({ account: '', smsCode: '', password: '', confirmPassword: '' });
      setForgotCountdown(0);
      setIsForgotSending(false);
      setBindForm({ phone: '', code: '' });
      setBindErrors({ phone: '', code: '' });
      setBindCountdown(0);
      setIsBindSending(false);
      setBindserData({});
      setBindLoading(false);
      setShowPassword(false);
      setShowConfirmPassword(false);
      setShowForgotPassword(false);
      setShowForgotConfirmPassword(false);
    }
    if (setLoginDialogOpen) {
      setLoginDialogOpen(open)
    }
  }
  const [loginMode, setLoginMode] = useState<'login' | 'register' | 'forgot' | 'bindPhone'>('login')
  // 注册表单状态
  const [registerForm, setRegisterForm] = useState({
    account: '',
    password: '',
    confirmPassword: '',
    smsCode: '',
  })
  const [registerErrors, setRegisterErrors] = useState({
    account: '',
    password: '',
    confirmPassword: '',
    smsCode: '',
  })
  const [registerCountdown, setRegisterCountdown] = useState(0)
  const [isRegisterSending, setIsRegisterSending] = useState(false)
  useEffect(() => {
    if (registerCountdown > 0) {
      const timer = setTimeout(() => setRegisterCountdown(registerCountdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [registerCountdown])
  const handleRegisterInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setRegisterForm(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }
  const handleRegisterSendSms = async () => {
    if (registerCountdown > 0) return
    if (!/^1[3-9]\d{9}$/.test(registerForm.account)) {
      setRegisterErrors(prev => ({ ...prev, account: '请输入正确的手机号' }))
      return
    }
    setIsRegisterSending(true)
    try {
      await baseApi.sendSMSCode({
        smsType: 1,
        phone: registerForm.account
      })
      setRegisterCountdown(60)
      setRegisterErrors(prev => ({ ...prev, smsCode: '' }))
      toast.success('验证码已发送')
    } catch {
      setRegisterErrors(prev => ({ ...prev, smsCode: '验证码发送失败' }))
    } finally {
      setIsRegisterSending(false)
    }
  }
  const validateRegisterForm = () => {
    const newErrors = {
      account: '',
      password: '',
      confirmPassword: '',
      smsCode: '',
    }
    let isValid = true
    if (!registerForm.account) {
      newErrors.account = '请输入手机号'
      isValid = false
    } else if (!/^1[3-9]\d{9}$/.test(registerForm.account)) {
      newErrors.account = '请输入正确的手机号'
      isValid = false
    }
    if (!registerForm.password) {
      newErrors.password = '请输入密码'
      isValid = false
    } else {
      // 至少包含两种：字母、数字、特殊字符
      const hasLetter = /[a-zA-Z]/.test(registerForm.password)
      const hasNumber = /\d/.test(registerForm.password)
      const hasSpecial = /[^a-zA-Z0-9]/.test(registerForm.password)
      const count = [hasLetter, hasNumber, hasSpecial].filter(Boolean).length
      if (count < 2 || registerForm.password.length < 6) {
        newErrors.password = '密码必须包含数字和字母或特殊字符，长度至少6位'
        isValid = false
      }
    }
    if (!registerForm.confirmPassword) {
      newErrors.confirmPassword = '请确认密码'
      isValid = false
    } else if (registerForm.password !== registerForm.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致'
      isValid = false
    }
    if (!registerForm.smsCode) {
      newErrors.smsCode = '请输入验证码'
      isValid = false
    }
    setRegisterErrors(newErrors)
    return isValid
  }
  const handleRegisterSubmit = async (e: React.FormEvent) => {
    if(isLoading) return;
    e.preventDefault()
    if (!validateRegisterForm()) return
    validateCallbackRef.current = registerFn;
    captchaInsRef.current && captchaInsRef.current.verify()
  }
  const registerFn = (validate: string) => {
    setIsLoading(true)
    baseApi.phoneRegister({
      phone: registerForm.account,
      password: registerForm.password,
      code: registerForm.smsCode,
      validate
    }).then((res: any) => {
      login({
        ...res.data.user,
        token: res.data.token
      })
      setLoginMode('login')
    }).catch(error => {
      // 可以加toast提示
    }).finally(() => {
      setIsLoading(false)
    })
  }
  // 忘记密码表单状态
  const [forgotForm, setForgotForm] = useState({
    account: '',
    smsCode: '',
    password: '',
    confirmPassword: ''
  })
  const [forgotErrors, setForgotErrors] = useState({
    account: '',
    smsCode: '',
    password: '',
    confirmPassword: ''
  })
  const [forgotCountdown, setForgotCountdown] = useState(0)
  const [isForgotSending, setIsForgotSending] = useState(false)
  useEffect(() => {
    if (forgotCountdown > 0) {
      const timer = setTimeout(() => setForgotCountdown(forgotCountdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [forgotCountdown])
  const handleForgotInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setForgotForm(prev => ({ ...prev, [name]: value }))
  }
  const handleForgotSendSms = async () => {
    if (forgotCountdown > 0) return
    if (!/^1[3-9]\d{9}$/.test(forgotForm.account)) {
      setForgotErrors(prev => ({ ...prev, account: '请输入正确的手机号' }))
      return
    }
    setIsForgotSending(true)
    try {
      await baseApi.sendSMSCode({
        phone: forgotForm.account,
        smsType: 3
      })
      setForgotCountdown(60)
      setForgotErrors(prev => ({ ...prev, smsCode: '' }))
      toast.success('验证码已发送')
    } catch {
      setForgotErrors(prev => ({ ...prev, smsCode: '验证码发送失败' }))
    } finally {
      setIsForgotSending(false)
    }
  }
  const validateForgotForm = () => {
    const newErrors = {
      account: '',
      smsCode: '',
      password: '',
      confirmPassword: ''
    }
    let isValid = true
    if (!forgotForm.account) {
      newErrors.account = '请输入手机号'
      isValid = false
    } else if (!/^1[3-9]\d{9}$/.test(forgotForm.account)) {
      newErrors.account = '请输入正确的手机号'
      isValid = false
    }
    if (!forgotForm.smsCode) {
      newErrors.smsCode = '请输入验证码'
      isValid = false
    }
    if (!forgotForm.password) {
      newErrors.password = '请输入新密码'
      isValid = false
    } else {
      // 至少包含两种：字母、数字、特殊字符
      const hasLetter = /[a-zA-Z]/.test(forgotForm.password)
      const hasNumber = /\d/.test(forgotForm.password)
      const hasSpecial = /[^a-zA-Z0-9]/.test(forgotForm.password)
      const count = [hasLetter, hasNumber, hasSpecial].filter(Boolean).length
      if (count < 2 || forgotForm.password.length < 6) {
        newErrors.password = '密码必须包含数字和字母或特殊字符，长度至少6位'
        isValid = false
      }
    }
    if (!forgotForm.confirmPassword) {
      newErrors.confirmPassword = '请确认新密码'
      isValid = false
    } else if (forgotForm.password !== forgotForm.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致'
      isValid = false
    }
    setForgotErrors(newErrors)
    return isValid
  }
  const handleForgotSubmit = async (e: React.FormEvent) => {
    if(isLoading) return;
    e.preventDefault()
    if (!validateForgotForm()) return
    setIsLoading(true)
    userApi.retrievePassword({
      phone: forgotForm.account,
      code: forgotForm.smsCode,
      newPassword: forgotForm.password
    }).then((res: any) => {
      if(res.code === 0) {
        toast.success('密码重置成功，请登录')
        setLoginMode('login')
      }
    }).catch((error: any) => {
      // 可以加toast提示
    }).finally(() => {
      setIsLoading(false)
    })
  }
  // 切换登录方式时清空输入和校验
  const handleLoginTypeChange = (type: 'password' | 'sms') => {
    setLoginType(type);
    setLoginForm({ account: '', password: '' });
    setLoginErrors({ account: '', password: '' });
    setSmsCode('');
    setSmsError('');
  };
  // 切换登录/注册/忘记密码模式时清空对应表单
  const handleLoginModeChange = (mode: 'login' | 'register' | 'forgot') => {
    setLoginMode(mode);
    setLoginForm({ account: '', password: '' });
    setLoginErrors({ account: '', password: '' });
    setSmsCode('');
    setSmsError('');
    setRegisterForm({ account: '', password: '', confirmPassword: '', smsCode: '' });
    setRegisterErrors({ account: '', password: '', confirmPassword: '', smsCode: '' });
    setForgotForm({ account: '', smsCode: '', password: '', confirmPassword: '' });
    setForgotErrors({ account: '', smsCode: '', password: '', confirmPassword: '' });
  };
  
  // 绑定手机号倒计时
  useEffect(() => {
    if (bindCountdown > 0) {
      const timer = setTimeout(() => setBindCountdown(bindCountdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [bindCountdown])

  const handleBindInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBindForm(prev => ({ ...prev, [name]: value }));
  };
  const handleBindSendSms = async () => {
    if (bindCountdown > 0) return;
    if (!/^1[3-9]\d{9}$/.test(bindForm.phone)) {
      setBindErrors(prev => ({ ...prev, phone: '请输入正确的手机号' }));
      return;
    }
    setIsBindSending(true);
    try {
      await baseApi.sendSMSCode({
        phone: bindForm.phone,
        smsType: 3, // 3 代表绑定手机号
      });
      setBindCountdown(60);
      setBindErrors(prev => ({ ...prev, code: '' }));
      toast.success('验证码已发送');
    } catch {
      setBindErrors(prev => ({ ...prev, code: '验证码发送失败' }));
    } finally {
      setIsBindSending(false);
    }
  };
  const validateBindForm = () => {
    const newErrors = { phone: '', code: '' };
    let isValid = true;
    if (!bindForm.phone) {
      newErrors.phone = '请输入手机号';
      isValid = false;
    } else if (!/^1[3-9]\d{9}$/.test(bindForm.phone)) {
      newErrors.phone = '请输入正确的手机号';
      isValid = false;
    }
    if (!bindForm.code) {
      newErrors.code = '请输入验证码';
      isValid = false;
    }
    setBindErrors(newErrors);
    return isValid;
  };
  const handleBindSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateBindForm()) return;
    setBindLoading(true);
    try {
      const res: any = await baseApi.bindWechatPhone({
        "phone": bindForm.phone,
        "code": bindForm.code,
        "openId": bindserData.openid,
        "unionId": bindserData.unionid,
        "nickName": bindserData.nickname,
        "avatar": bindserData.headimgurl
      });
      login({
        ...res.data.user,
        token: res.data.token,
      });
      if (setLoginDialogOpen) setLoginDialogOpen(false);
    } catch {
      // 可以加toast提示
    } finally {
      setBindLoading(false);
    }
  };
  return (
    <Modal open={loginDialogOpen} width={'auto'} centered onCancel={() => handleOpenChange(false)} footer={null}>
      <div className="flex min-h-[420px] rounded-xl overflow-hidden bg-background">
        {/* 左侧表单 */}
        <div className="flex-1 bg-card p-8 pt-10 pb-8 flex flex-col justify-center">
          {loginMode === 'login' && (
            <>
              <div className="mb-6 flex gap-6 justify-center text-lg font-medium">
                <span
                  className={loginType === 'password' ? 'text-primary border-b-2 border-primary pb-1 cursor-pointer' : 'text-muted-foreground cursor-pointer'}
                  onClick={() => handleLoginTypeChange('password')}
                >密码登录</span>
                <span
                  className={loginType === 'sms' ? 'text-primary border-b-2 border-primary pb-1 cursor-pointer' : 'text-muted-foreground cursor-pointer'}
                  onClick={() => handleLoginTypeChange('sms')}
                >验证码登录</span>
              </div>
              {loginType === 'password' && (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <div className="relative">
                      <User theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="account"
                        name="account"
                        type="text"
                        placeholder="请输入手机号"
                        className={`text-muted-foreground pl-10 ${(loginErrors.account && !loginForm.account) ? 'border-red-500' : ''}`}
                        value={loginForm.account}
                        onChange={handleLoginInputChange}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="relative">
                      <Lock theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="password"
                        name="password"
                        type="password"
                        placeholder="请输入密码"
                        className={`text-muted-foreground pl-10 ${(loginErrors.password && !loginForm.password) ? 'border-red-500' : ''}`}
                        value={loginForm.password}
                        onChange={handleLoginInputChange}
                      />
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground mb-2">
                    注册/登录即代表已阅读并同意我们的 <a href="/user-agreement" className="text-primary underline" target="_blank" rel="noopener noreferrer">用户协议</a> 与 <a href="/privacy-policy" className="text-primary underline" target="_blank" rel="noopener noreferrer">隐私政策</a>
                  </div>
                  <Button type="submit" className="w-full" disabled={isLoading}>登录</Button>
                </form>
              )}
              {loginType === 'sms' && (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <div className="relative">
                      <User theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="sms-account"
                        name="account"
                        type="text"
                        placeholder="请输入手机号"
                        className={`text-muted-foreground pl-10 ${(loginErrors.account && !loginForm.account) ? 'border-red-500' : ''}`}
                        value={loginForm.account}
                        onChange={handleLoginInputChange}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        id="sms-code"
                        name="smsCode"
                        type="text"
                        placeholder="请输入验证码"
                        className={`text-muted-foreground ${smsError && !smsCode ? 'border-red-500' : ''}`}
                        value={smsCode}
                        onChange={e => setSmsCode(e.target.value)}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="w-32 text-muted-foreground"
                        onClick={handleSendSmsCode}
                        disabled={smsCountdown > 0 || isSendingSms}
                      >
                        {smsCountdown > 0 ? `${smsCountdown}秒后重试` : isSendingSms ? "发送中..." : "获取验证码"}
                      </Button>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground mb-2">
                    注册/登录即代表已阅读并同意我们的 <a href="/user-agreement" className="text-primary underline" target="_blank" rel="noopener noreferrer">用户协议</a> 与 <a href="/privacy-policy" className="text-primary underline" target="_blank" rel="noopener noreferrer">隐私政策</a>
                  </div>
                  <Button type="submit" className="w-full" disabled={isLoading}>登录</Button>
                </form>
              )}
              <div className="flex justify-between mt-2 text-xs">
                <a href="#" className="text-primary" onClick={e => {e.preventDefault(); handleLoginModeChange('forgot')}}>忘记密码</a>
                <a href="#" className="text-primary" onClick={e => {e.preventDefault(); handleLoginModeChange('register')}}>立即注册</a>
              </div>
            </>
          )}
          {loginMode === 'register' && (
            <>
              <div className="mb-6 flex gap-6 justify-center text-lg font-medium">
                <span className="text-primary border-b-2 border-primary pb-1">注册</span>
              </div>
              <form onSubmit={handleRegisterSubmit} className="space-y-4">
                <div className="space-y-2">
                  <div className="relative">
                    <User theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="register-account"
                      name="account"
                      type="text"
                      placeholder="请输入手机号"
                      className={`text-muted-foreground pl-10 ${registerErrors.account ? 'border-red-500' : ''}`}
                      value={registerForm.account}
                      onChange={handleRegisterInputChange}
                    />
                  </div>
                  {registerErrors.account && <p className="text-sm text-red-500">{registerErrors.account}</p>}
                </div>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      id="register-sms-code"
                      name="smsCode"
                      type="text"
                      placeholder="请输入验证码"
                      className={`text-muted-foreground ${registerErrors.smsCode ? 'border-red-500' : ''}`}
                      value={registerForm.smsCode}
                      onChange={handleRegisterInputChange}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      className="w-32 text-muted-foreground"
                      onClick={handleRegisterSendSms}
                      disabled={registerCountdown > 0 || isRegisterSending}
                    >
                      {registerCountdown > 0 ? `${registerCountdown}秒后重试` : isRegisterSending ? '发送中...' : '获取验证码'}
                    </Button>
                  </div>
                  {registerErrors.smsCode && <p className="text-sm text-red-500">{registerErrors.smsCode}</p>}
                </div>
                <div className="space-y-2">
                  <div className="relative">
                    <Lock theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="register-password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="请设置密码"
                      className={`text-muted-foreground pl-10 pr-10 ${(registerErrors.password) ? 'border-red-500' : ''}`}
                      value={registerForm.password}
                      onChange={handleRegisterInputChange}
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                          <line x1="1" y1="1" x2="23" y2="23"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      )}
                    </button>
                  </div>
                  {registerErrors.password && <p className="text-sm text-red-500">{registerErrors.password}</p>}
                </div>
                <div className="space-y-2">
                  <div className="relative">
                    <Lock theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="register-confirm-password"
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="请确认密码"
                      className={`text-muted-foreground pl-10 pr-10 ${(registerErrors.confirmPassword) ? 'border-red-500' : ''}`}
                      value={registerForm.confirmPassword}
                      onChange={handleRegisterInputChange}
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                          <line x1="1" y1="1" x2="23" y2="23"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      )}
                    </button>
                  </div>
                  {registerErrors.confirmPassword && <p className="text-sm text-red-500">{registerErrors.confirmPassword}</p>}
                </div>
                <div className="text-xs text-muted-foreground mb-2">注册/登录即代表已阅读并同意我们的 <a href="/user-agreement" className="text-primary underline" target="_blank" rel="noopener noreferrer">用户协议</a> 与 <a href="/privacy-policy" className="text-primary underline" target="_blank" rel="noopener noreferrer">隐私政策</a></div>
                <Button type="submit" className="w-full" disabled={isLoading}>注册</Button>
              </form>
              <div className="flex justify-between mt-2 text-xs">
                <a href="#" className="text-primary" onClick={e => {e.preventDefault(); handleLoginModeChange('login')}}>已有账号？去登录</a>
              </div>
            </>
          )}
          {loginMode === 'forgot' && (
            <>
              <div className="mb-6 flex gap-6 justify-center text-lg font-medium">
                <span className="text-primary border-b-2 border-primary pb-1">找回密码</span>
              </div>
              <form onSubmit={handleForgotSubmit} className="space-y-4">
                <div className="space-y-2">
                  <div className="relative">
                    <User theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="forgot-account"
                      name="account"
                      type="text"
                      placeholder="请输入手机号"
                      className={`text-muted-foreground pl-10 ${forgotErrors.account ? 'border-red-500' : ''}`}
                      value={forgotForm.account}
                      onChange={handleForgotInputChange}
                    />
                  </div>
                  {forgotErrors.account && <p className="text-sm text-red-500">{forgotErrors.account}</p>}
                </div>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      id="forgot-sms-code"
                      name="smsCode"
                      type="text"
                      placeholder="请输入验证码"
                      className={`text-muted-foreground ${forgotErrors.smsCode? 'border-red-500' : ''}`}
                      value={forgotForm.smsCode}
                      onChange={handleForgotInputChange}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      className="w-32 text-muted-foreground"
                      onClick={handleForgotSendSms}
                      disabled={forgotCountdown > 0 || isForgotSending}
                    >
                      {forgotCountdown > 0 ? `${forgotCountdown}秒后重试` : isForgotSending ? '发送中...' : '获取验证码'}
                    </Button>
                  </div>
                  {forgotErrors.smsCode && <p className="text-sm text-red-500">{forgotErrors.smsCode}</p>}
                </div>
                <div className="space-y-2">
                  <div className="relative">
                    <Lock theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="forgot-password"
                      name="password"
                      type={showForgotPassword ? "text" : "password"}
                      placeholder="请输入新密码"
                      className={`text-muted-foreground pl-10 pr-10 ${forgotErrors.password ? 'border-red-500' : ''}`}
                      value={forgotForm.password}
                      onChange={handleForgotInputChange}
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowForgotPassword(!showForgotPassword)}
                    >
                      {showForgotPassword ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                          <line x1="1" y1="1" x2="23" y2="23"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      )}
                    </button>
                  </div>
                  {forgotErrors.password && <p className="text-sm text-red-500">{forgotErrors.password}</p>}
                </div>
                <div className="space-y-2">
                  <div className="relative">
                    <Lock theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="forgot-confirm-password"
                      name="confirmPassword"
                      type={showForgotConfirmPassword ? "text" : "password"}
                      placeholder="请确认新密码"
                      className={`text-muted-foreground pl-10 pr-10 ${forgotErrors.confirmPassword ? 'border-red-500' : ''}`}
                      value={forgotForm.confirmPassword}
                      onChange={handleForgotInputChange}
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowForgotConfirmPassword(!showForgotConfirmPassword)}
                    >
                      {showForgotConfirmPassword ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                          <line x1="1" y1="1" x2="23" y2="23"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      )}
                    </button>
                  </div>
                  {forgotErrors.confirmPassword && <p className="text-sm text-red-500">{forgotErrors.confirmPassword}</p>}
                </div>
                <Button type="submit" className="w-full" disabled={isLoading}>重置密码</Button>
              </form>
              <div className="flex justify-between mt-2 text-xs">
                <a href="#" className="text-primary" onClick={e => {e.preventDefault(); handleLoginModeChange('login')}}>返回登录</a>
              </div>
            </>
          )}
          {loginMode === 'bindPhone' && (
            <>
              <div className="mb-6 flex gap-6 justify-center text-lg font-medium">
                <span className="text-primary border-b-2 border-primary pb-1">绑定手机号</span>
              </div>
              <form onSubmit={handleBindSubmit} className="space-y-4">
                <div className="space-y-2">
                  <div className="relative">
                    <User theme="outline" size="16" className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="bind-phone"
                      name="phone"
                      type="text"
                      placeholder="请输入手机号"
                      className={`text-muted-foreground pl-10 ${(bindErrors.phone && !bindForm.phone) ? 'border-red-500' : ''}`}
                      value={bindForm.phone}
                      onChange={handleBindInputChange}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      id="bind-code"
                      name="code"
                      type="text"
                      placeholder="请输入验证码"
                      className={`text-muted-foreground ${bindErrors.code && !bindForm.code ? 'border-red-500' : ''}`}
                      value={bindForm.code}
                      onChange={handleBindInputChange}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      className="w-32 text-muted-foreground"
                      onClick={handleBindSendSms}
                      disabled={bindCountdown > 0 || isBindSending}
                    >
                      {bindCountdown > 0 ? `${bindCountdown}秒后重试` : isBindSending ? '发送中...' : '获取验证码'}
                    </Button>
                  </div>
                </div>
                <Button type="submit" className="w-full" disabled={bindLoading}>绑定并登录</Button>
              </form>
            </>
          )}
        </div>
        {/* 右侧二维码 */}
        <div
          className={`w-[260px] ${(loginMode === 'login' && (loginType === 'password' || loginType === 'sms')) ? 'flex' : 'hidden'} flex-col items-center justify-center border-l border-border bg-card`}
        >
          <div id="wxCode" className="relative" style={{width: '160px', height: '160px'}}></div>
          <div className="flex items-center gap-2 mt-2">
            <Wechat theme="filled" size="20" fill="#1AAD19" />
            <span className="text-muted-foreground text-sm">微信登录</span>
          </div>
        </div>
      </div>
    </Modal>
  )
}
