"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Search, Home, Computer, Book, Workbench, People, ShoppingCart, Wallet, Setting, MoreApp } from "@icon-park/react"

type categoryItem = {
  value: string
  label: string
  projectsCount: number
  [key: string]: any
}

type FilterSidebarProps = {
  selectedCategory: categoryItem
  setSelectedCategory: (category: any) => void
  clearFilters: () => void
  categories: any[]
  setCurrentPage: (page: number) => void
}

// 翻译类别名称
const categoryTranslations = {
  all: "全部",
  life: "生活服务",
  computer: "电脑操作", 
  knowledge: "个人知识",
  business: "商业效率",
  social: "社交媒体",
  ecommerce: "电商平台",
  finance: "金融服务",
  development: "技术开发",
  other: "其他"
}

// 类别图标映射
const categoryIcons = {
  life: Home,
  computer: Computer,
  knowledge: Book,
  business: Workbench,
  social: People,
  ecommerce: ShoppingCart,
  finance: Wallet,
  development: Setting,
  other: MoreApp
}

export function FilterSidebar({ selectedCategory, setSelectedCategory, clearFilters, categories, setCurrentPage }: FilterSidebarProps) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold mb-2">类别</h2>
        <div className="flex flex-col space-y-2">
          {categories && categories.length > 0 ? categories.map((category: categoryItem) => {
            // 处理不同的数据结构
            const categoryValue = category.value || category.sysDictionaryDetailValue || category.name || "other"
            const categoryLabel = category.label || category.sysDictionaryDetailLabel || category.description || categoryValue
            const projectsCount = category.projectsCount || category.projects_count || category.count || 0
            
            const IconComponent = categoryValue === "all" ? Search : categoryIcons[categoryValue as keyof typeof categoryIcons] || MoreApp
            const displayLabel = categoryTranslations[categoryValue as keyof typeof categoryTranslations] || categoryLabel
            
            return (
              <Button
                key={categoryValue}
                variant={selectedCategory.value === categoryValue ? "default" : "outline"}
                className={`flex items-center justify-between h-10 px-3 ${selectedCategory.value === categoryValue ? "bg-primary text-primary-foreground" : "hover-orange"}`}
                onClick={() => {
                  let data = {
                    value: categoryValue,
                    label: categoryLabel,
                    projectsCount: projectsCount
                  }
                  setSelectedCategory(data)
                  setCurrentPage(1)
                  
                }}
              >
                <div className="flex items-center">
                  <div className="mr-2 icon-park-container">
                    <IconComponent theme="outline" size="18" />
                  </div>
                  <div className="text-sm">
                    {displayLabel}
                  </div>
                </div>
                <div className="text-xs opacity-70">{projectsCount}</div>
              </Button>
            )
          }) : (
            <div className="text-sm text-muted-foreground">
              暂无分类数据
            </div>
          )}
        </div>
      </div>

      <div className="pt-4">
        <Button variant="outline" className="w-full hover-orange" onClick={clearFilters}>
          清除筛选条件
        </Button>
      </div>
    </div>
  )
}
