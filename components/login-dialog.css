#wxCode iframe{
  width: 160px;
  height: 160px;
}

/* Antd Modal 暗色模式适配 */
.ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.65) !important;
}

.dark .ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.75) !important;
}

.ant-modal-content {
  background-color: hsl(var(--card)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 12px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.dark .ant-modal-content {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5) !important;
}

.ant-modal-close {
  color: hsl(var(--muted-foreground)) !important;
}

.ant-modal-close:hover {
  color: hsl(var(--foreground)) !important;
}

.ant-modal-body {
  padding: 0 !important;
}

/* 微信二维码区域的边框适配 */
.dark #wxCode iframe {
  filter: brightness(0.9) contrast(1.1);
}