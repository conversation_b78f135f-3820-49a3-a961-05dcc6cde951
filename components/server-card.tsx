import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Computer, Earth, Like } from "@icon-park/react"

type ServerCardProps = {
  server: {
    descriptionChinese: any
    name: string
    description: string
    package: string
    logoUrl: string
    callMethod: string[]
    offlineUsageCount: number
    onlineUsageCount: number
    provider: string
    likes: number
    uuid: number
    isLiked: boolean
  }
  sessionCatchData: () => void
}

export function ServerCard({ server, sessionCatchData }: ServerCardProps) {
  return (
    <Link href={`/server/${server.uuid}`} className="block" onClick={sessionCatchData}>
      <Card className="relative card-hover cursor-pointer shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden h-full flex flex-col">
        {/* 右上角图标 */}
        <div className="absolute top-3 right-3 flex items-center gap-2 z-10">
          {server.callMethod.includes("local") && (
            <div className="tooltip-trigger">
              <Computer theme="outline" size="16" className="text-primary" />
              <div className="custom-tooltip absolute top-full right-0 mt-2 px-2 py-1 bg-black text-white text-xs rounded pointer-events-none whitespace-nowrap z-50">
                本地安装 {server.offlineUsageCount.toLocaleString()} 次
              </div>
            </div>
          )}
          {server.callMethod.includes("online") && (
            <div className="tooltip-trigger">
              <Earth theme="outline" size="16" className="text-primary" />
              <div className="custom-tooltip absolute top-full right-0 mt-2 px-2 py-1 bg-black text-white text-xs rounded pointer-events-none whitespace-nowrap z-50">
                在线调用 {server.onlineUsageCount.toLocaleString()} 次
              </div>
            </div>
          )}
        </div>

        <CardHeader className="p-5 pb-3">
          <div className="flex items-start gap-3">
            <div className="w-10 h-10 rounded-md overflow-hidden flex-shrink-0">
              <img
                src={server.logoUrl || "/placeholder.svg"}
                alt={`${server.name} logo`}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <CardTitle className="text-lg font-bold text-primary">{server.name}</CardTitle>
              <CardDescription className="text-xs text-muted-foreground mt-1">{server.package}</CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="px-5 py-2 flex-1">
          <p className="text-sm line-clamp-3 mb-3" dangerouslySetInnerHTML={{ __html: server.descriptionChinese? server.descriptionChinese : server.description }}></p>
        </CardContent>

        <div className="mt-auto border-t">
          <div className="px-5 py-3 flex items-center justify-between">
            <div className="text-xs text-muted-foreground">{server.provider}</div>
            <div className="tooltip-trigger relative flex items-center gap-1 text-muted-foreground">
              <Like
                theme={server.isLiked ? "filled" : "outline"}
                size="16"
                className={`
                  ${server.isLiked ? "text-red-500" : ""} 
                  transition-all duration-300
                `}
              />
              <span className="text-xs">{server.likes}</span>
              <div className="custom-tooltip absolute bottom-full right-0 mb-2 px-2 py-1 bg-black text-white text-xs rounded pointer-events-none whitespace-nowrap z-50">
                点赞数
              </div>
            </div>
          </div>
        </div>
      </Card>
    </Link>
  )
}
