"use client"

import { ThemeToggle } from "@/components/theme-toggle"
import { MainNav } from "@/components/main-nav"
import { She<PERSON>, <PERSON>et<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { HamburgerButton, User, Plus } from "@icon-park/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useMediaQuery } from "@/hooks/use-media-query"
import { useState, useEffect } from "react"
import { UserDropdown } from "@/components/user-dropdown"
import { useAuth } from "@/contexts/auth-context"
import Link from "next/link"
import { LoginDialog } from "@/components/login-dialog"

export function SiteHeader() {
  const isMobile = useMediaQuery("(max-width: 768px)")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { isAuthenticated, isLoading, loginDialogOpen, setLoginDialogOpen } = useAuth()
  // 关闭菜单的函数，将传递给MainNav
  const closeMenu = () => setIsMenuOpen(false)

  // 当屏幕尺寸变化时，关闭菜单
  useEffect(() => {
    if (!isMobile && isMenuOpen) {
      setIsMenuOpen(false)
    }
  }, [isMobile, isMenuOpen])

  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center gap-3">
            <Link href="/" className="flex items-center">
              <div className="flex items-center">
                <img src="/logo_light.png" alt="MCP Logo" className="h-8 dark:hidden" />
                <img src="/logo_dark.png" alt="MCP Logo" className="h-8 hidden dark:block" />
              </div>
            </Link>
          </div>

          {/* 桌面导航 - 在移动设备上隐藏 */}
          <div className="hidden md:flex flex-1 justify-start ml-8">
            <MainNav />
          </div>

          {/* 桌面主题切换和登录/用户按钮 - 在移动设备上隐藏 */}
          <div className="hidden md:flex items-center gap-6">
            {/* <Link
              href="/submit-server"
              className="flex items-center gap-1.5 text-sm font-medium transition-colors hover:text-primary text-muted-foreground"
            >
              <Plus theme="outline" size="16" />
              <span>提交MCP</span>
            </Link> */}
            <ThemeToggle />
            {isLoading ? (
              <div className="w-24 h-8 bg-muted/30 animate-pulse rounded-md"></div>
            ) : isAuthenticated ? (
              <UserDropdown />
            ) : (
              <div
                className="flex items-center gap-1.5 text-muted-foreground hover:text-primary cursor-pointer transition-colors"
                onClick={() => setLoginDialogOpen(true)}
              >
                <User theme="outline" size="16" />
                <span className="font-medium">登录</span>
              </div>
            )}
          </div>

          {/* 移动设备菜单按钮 - 在桌面上隐藏 */}
          <div className="flex md:hidden">
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" aria-label="菜单" className="bg-primary text-primary-foreground">
                  <HamburgerButton theme="outline" size="24" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[250px] sm:w-[300px]">
                <div className="flex flex-col py-6 h-full">
                  <MainNav isMobile={true} closeMenu={closeMenu} />

                  {/* 在抽屉中添加登录/用户按钮和主题切换按钮 */}
                  <div className="mt-auto pt-6 border-t space-y-4">
                    <div className="flex flex-col space-y-2">
                      <span className="text-sm font-medium">{isAuthenticated ? "用户账户" : "登录账户"}</span>
                      {isLoading ? (
                        <div className="w-full h-8 bg-muted/30 animate-pulse rounded-md"></div>
                      ) : isAuthenticated ? (
                        <UserDropdown isMobile={true} />
                      ) : (
                        <div
                          className="flex items-center gap-1.5 text-muted-foreground hover:text-primary cursor-pointer transition-colors p-2"
                          onClick={() => {
                            setLoginDialogOpen(true)
                            setIsMenuOpen(false) // 关闭菜单
                          }}
                        >
                          <User theme="outline" size="16" />
                          <span className="font-medium">登录</span>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">切换主题</span>
                      <ThemeToggle />
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </header>
      <div id="captcha" style={{zIndex: 999999, pointerEvents: 'auto'}} onClick={(e) => {
        e.stopPropagation()
        e.preventDefault()
        console.log('captcha')
      }}></div>
      {/* 登录对话框 */}
      <LoginDialog />
    </>
  )
}
