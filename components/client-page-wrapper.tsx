"use client"

import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { ClientHomePage } from "@/components/client-home-page"

interface ClientPageWrapperProps {
  initialProjects: any[]
  initialTotalCount: number
  initialCategories: any[]
  totalProjects: number
}

export function ClientPageWrapper({ 
  initialProjects, 
  initialTotalCount, 
  initialCategories, 
  totalProjects 
}: ClientPageWrapperProps) {
  return (
    <>
      <SiteHeader />
      <div className="gradient-bg min-h-screen">
        <ClientHomePage 
          initialProjects={initialProjects}
          initialTotalCount={initialTotalCount}
          initialCategories={initialCategories}
          totalProjects={totalProjects}
        />
      </div>
      <SiteFooter />
    </>
  )
} 