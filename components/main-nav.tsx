"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

const navItems = [
  {
    name: "MCP服务器",
    href: "/",
  },
  {
    name: "MCP客户端",
    href: "/clients",
  },
  {
    name: "应用案例",
    href: "/use-cases",
  },
]

interface MainNavProps {
  isMobile?: boolean
  closeMenu?: () => void
}

export function MainNav({ isMobile, closeMenu }: MainNavProps) {
  const pathname = usePathname()
  // 处理链接点击，如果是移动设备且提供了closeMenu函数，则调用它
  const handleLinkClick = () => {
    if (isMobile && closeMenu) {
      closeMenu()
    }
  }

  return isMobile ? (
    // 移动设备上的垂直导航
    <nav className="flex flex-col space-y-2">
      <h2 className="text-lg font-bold mb-4">导航菜单</h2>
      {navItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            "text-base font-medium transition-colors hover:text-primary py-3 px-2 rounded-md",
            pathname === item.href
              ? "text-primary font-semibold bg-primary/10"
              : "text-muted-foreground hover:bg-muted/50",
          )}
          onClick={handleLinkClick}
        >
          {item.name}
        </Link>
      ))}
      {/* <Link
        href="/submit-server"
        className="flex items-center gap-1.5 text-muted-foreground hover:text-primary transition-colors"
      >
        <Plus theme="outline" size="16" />
        <span>提交MCP</span>
      </Link> */}
    </nav>
  ) : (
    // 桌面上的水平导航
    <nav className="flex items-center space-x-6">
      {navItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            "text-sm font-medium transition-colors hover:text-primary",
            pathname === item.href
              ? "text-primary font-semibold border-b-2 border-primary pb-1"
              : "text-muted-foreground",
          )}
        >
          {item.name}
        </Link>
      ))}
    </nav>
  )
}
