"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {Tooltip} from "antd"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Server, Key, Logout, Setting, Help } from "@icon-park/react"
import userApi from "@/lib/api/apis/user"
export function UserDropdown({ isMobile = false }: { isMobile?: boolean }) {
  const [points, setPoints] = useState(0)
  
  const { user, logout } = useAuth()
  const getPoints = async () => {
    userApi.getUserPoints().then((res: any) => {
      setPoints(res.data.totalPoints)
    })
  }
  if (!user) return null

  if (isMobile) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Link href="/my-servers" className="flex items-center gap-2 text-sm hover:text-primary">
            <Server theme="outline" size="16" />
            <span>我的MCP服务</span>
          </Link>
          <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
            {user.points} 积分
          </Badge>
        </div>
        {/* <Link href="/api-keys" className="flex items-center gap-2 text-sm hover:text-primary">
          <Key theme="outline" size="16" />
          <span>API密钥</span>
        </Link> */}
        <div className="flex items-center gap-2 text-sm hover:text-primary cursor-pointer" onClick={logout}>
          <Logout theme="outline" size="16" />
          <span>退出登录</span>
        </div>
      </div>
    )
  }

  return (
    <DropdownMenu onOpenChange={(e) => {
      if(e) getPoints();
    }} >
      <DropdownMenuTrigger className="flex items-center gap-2 outline-none">
        <Avatar className="h-8 w-8 border">
          <AvatarImage src={user.headerImg} alt={user.nickName} />
          <AvatarFallback>{user.nickName}</AvatarFallback>
        </Avatar>
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">{user.nickName}</span>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="flex justify-between items-center">
          <span>我的账户</span>
          <Badge variant="outline" className="ml-2 bg-primary/10 text-primary border-primary/20">
            {points} 积分
            <Tooltip title="每日登录赠送50积分">
              <Help theme="outline" size="16" className="ml-2" />
            </Tooltip>
          </Badge>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <Link href="/my-servers">
          <DropdownMenuItem className="cursor-pointer">
            <Server theme="outline" size="16" className="mr-2" />
            <span>我的MCP服务</span>
          </DropdownMenuItem>
        </Link>
        
        <Link href="/settings">
          <DropdownMenuItem className="cursor-pointer">
            <Setting theme="outline" size="16" className="mr-2" />
            <span>个人设置</span>
          </DropdownMenuItem>
        </Link>
        
        {/* <Link href="/api-keys">
          <DropdownMenuItem className="cursor-pointer">
            <Key theme="outline" size="16" className="mr-2" />
            <span>API密钥</span>
          </DropdownMenuItem>
        </Link> */}
        <DropdownMenuSeparator />
        <DropdownMenuItem className="cursor-pointer text-red-500" onClick={logout}>
          <Logout theme="outline" size="16" className="mr-2" />
          <span>退出登录</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
