"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { categoryTranslations } from "@/lib/data"
import { Left, Upload, Delete } from "@icon-park/react"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from "@/components/ui/command"
import { Pop<PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { serverData } from "@/lib/data"
import { Check } from "@icon-park/react"

export default function SubmitServerPage() {
  const router = useRouter()
  const { isAuthenticated, isLoading, setLoginDialogOpen } = useAuth()

  // 表单状态
  const [formData, setFormData] = useState({
    package: "",
    name: "",
    description: "",
    category: "",
    tags: [] as string[],
    owner: "",
    github: "",
    website: "",
    detail: "",
    logo: "",
    type: "node", // This corresponds to the server's type field for language
    publishType: "npm", // New field to track npm vs GitHub/Gitee selection
  })

  // 表单错误
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [tagInput, setTagInput] = useState("")
  const [tagPopoverOpen, setTagPopoverOpen] = useState(false)
  const [filteredTags, setFilteredTags] = useState<string[]>([])

  // 提取所有唯一的标签
  const allUniqueTags = Array.from(new Set(serverData.flatMap((server) => server.tags))).sort()

  // 如果未登录，显示登录对话框
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      setLoginDialogOpen(true)
    }
  }, [isLoading, isAuthenticated])

  // 处理表单字段变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))

    // 清除错误
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  // 处理标签输入变化
  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setTagInput(value)

    // 过滤匹配的标签
    if (value.trim()) {
      const matched = allUniqueTags.filter(
        (tag) => tag.toLowerCase().includes(value.toLowerCase()) && !formData.tags.includes(tag),
      )
      setFilteredTags(matched)
      if (matched.length > 0) {
        setTagPopoverOpen(true)
      }
    } else {
      setFilteredTags([])
      setTagPopoverOpen(false)
    }
  }

  // 处理标签选择
  const handleTagSelect = (tag: string) => {
    setTagInput(tag)
    setTagPopoverOpen(false)
  }

  // 处理标签添加
  const handleAddTag = () => {
    if (formData.tags.length >= 5) {
      setErrors((prev) => ({ ...prev, tags: "最多只能添加5个标签" }))
      return
    }

    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }))
      setTagInput("")
      setTagPopoverOpen(false)
      setFilteredTags([])

      // 清除标签错误
      if (errors.tags) {
        setErrors((prev) => {
          const newErrors = { ...prev }
          delete newErrors.tags
          return newErrors
        })
      }
    }
  }

  // 处理标签删除
  const handleRemoveTag = (tag: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((t) => t !== tag),
    }))
  }

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) newErrors.name = "请输入名称"
    if (!formData.description.trim()) newErrors.description = "请输入描述"
    if (!formData.category) newErrors.category = "请选择类别"
    if (!formData.owner.trim()) newErrors.owner = "请输入作者"
    if (!formData.detail.trim()) newErrors.detail = "请输入详细介绍"
    if (!formData.type) newErrors.type = "请选择程序类型"

    // Conditional validation based on language type and publish type
    // 验证发布方式相关字段
    if (formData.publishType === "npm" && !formData.package.trim()) {
      newErrors.package = "请输入包名"
    } else if (formData.publishType === "github" && !formData.github.trim()) {
      newErrors.github = "请输入项目代码链接"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsSubmitting(true)

    try {
      // 这里是模拟提交，实际应用中应该调用API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 提交成功后跳转回首页
      router.push("/")
    } catch (error) {
      console.error("提交失败:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  if (isLoading) {
    return (
      <>
        <SiteHeader />
        <div className="container mx-auto py-12 px-4 gradient-bg min-h-screen">
          <div className="max-w-3xl mx-auto">
            <div className="w-full h-12 bg-muted/30 animate-pulse rounded-md mb-4"></div>
            <div className="w-full h-64 bg-muted/30 animate-pulse rounded-md"></div>
          </div>
        </div>
        <SiteFooter />
      </>
    )
  }

  return (
    <>
      <SiteHeader />
      <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
        <div className="max-w-3xl mx-auto">
          <div className="mb-6">
            <Link href="/" className="inline-flex items-center text-muted-foreground hover:text-primary">
              <Left theme="outline" size="16" className="mr-1" />
              <span>返回首页</span>
            </Link>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">提交MCP Server</CardTitle>
              <CardDescription>分享您的MCP Server，让更多人了解和使用它</CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">
                      名称 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="MCP Server的名称"
                      className={errors.name ? "border-red-500" : ""}
                    />
                    {errors.name && <p className="text-xs text-red-500">{errors.name}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="owner">
                      作者 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="owner"
                      name="owner"
                      value={formData.owner}
                      onChange={handleChange}
                      placeholder="个人昵称或公司名"
                      className={errors.owner ? "border-red-500" : ""}
                    />
                    {errors.owner && <p className="text-xs text-red-500">{errors.owner}</p>}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">
                    描述 <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="一句话描述您MCP Server的功能"
                    className={errors.description ? "border-red-500" : ""}
                  />
                  {errors.description && <p className="text-xs text-red-500">{errors.description}</p>}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="category">
                      类别 <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.category} onValueChange={(value) => handleSelectChange("category", value)}>
                      <SelectTrigger className={errors.category ? "border-red-500" : ""}>
                        <SelectValue placeholder="选择类别" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(categoryTranslations)
                          .filter(([key]) => key !== "all")
                          .map(([key, value]) => (
                            <SelectItem key={key} value={key}>
                              {value}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    {errors.category && <p className="text-xs text-red-500">{errors.category}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website">官网链接</Label>
                    <Input
                      id="website"
                      name="website"
                      value={formData.website}
                      onChange={handleChange}
                      placeholder="如有官网，填写官网URL"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>标签</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Popover open={tagPopoverOpen && filteredTags.length > 0} onOpenChange={setTagPopoverOpen}>
                        <PopoverTrigger asChild>
                          <div className="flex-1">
                            <Input
                              value={tagInput}
                              onChange={handleTagInputChange}
                              onFocus={() => {
                                if (tagInput.trim() && filteredTags.length > 0) {
                                  setTagPopoverOpen(true)
                                }
                              }}
                              placeholder="输入标签"
                              className="w-full"
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  e.preventDefault()
                                  handleAddTag()
                                } else if (e.key === "Escape") {
                                  setTagPopoverOpen(false)
                                }
                              }}
                              disabled={formData.tags.length >= 5}
                            />
                          </div>
                        </PopoverTrigger>
                        <PopoverContent className="p-0 w-[300px]" align="start">
                          <Command>
                            <CommandList>
                              <CommandEmpty>没有匹配的标签</CommandEmpty>
                              <CommandGroup>
                                {filteredTags.map((tag) => (
                                  <CommandItem
                                    key={tag}
                                    onSelect={() => handleTagSelect(tag)}
                                    className="flex items-center gap-2 cursor-pointer"
                                  >
                                    <span>{tag}</span>
                                    {tagInput === tag && <Check theme="outline" size="16" className="ml-auto" />}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>
                    <Button type="button" onClick={handleAddTag} disabled={formData.tags.length >= 5}>
                      添加
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">最多添加5个标签 ({formData.tags.length}/5)</p>
                  {errors.tags && <p className="text-xs text-red-500">{errors.tags}</p>}
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.tags.map((tag) => (
                      <div
                        key={tag}
                        className="flex items-center gap-1 bg-primary/10 text-primary px-2 py-1 rounded-md"
                      >
                        <span>{tag}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="text-primary hover:text-primary/80"
                        >
                          <Delete theme="outline" size="14" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="logo">Logo</Label>
                  <Input
                    id="logo"
                    name="logo"
                    value={formData.logo}
                    onChange={handleChange}
                    placeholder="LOGO图片的URL"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="detail">
                    详细介绍 <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="detail"
                    name="detail"
                    value={formData.detail}
                    onChange={handleChange}
                    placeholder="详细描述您MCP Server的功能，支持Markdown格式"
                    className={`min-h-[200px] ${errors.detail ? "border-red-500" : ""}`}
                  />
                  {errors.detail && <p className="text-xs text-red-500">{errors.detail}</p>}
                </div>

                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="type">
                      开发语言 <span className="text-red-500">*</span>
                    </Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div
                        className={`border rounded-md p-4 flex items-center gap-3 cursor-pointer ${
                          formData.type === "node"
                            ? "border-primary bg-primary/5"
                            : "border-border hover:border-primary/50"
                        }`}
                        onClick={() => handleSelectChange("type", "node")}
                      >
                        <div className="w-8 h-8 flex items-center justify-center">
                          <img src="/assets/nodejs.svg" alt="Node.js" width={28} height={28} />
                        </div>
                        <div className="flex-1">Node.js</div>
                        {formData.type === "node" && (
                          <div className="w-5 h-5 rounded-full border-2 border-primary flex items-center justify-center">
                            <div className="w-2.5 h-2.5 rounded-full bg-primary"></div>
                          </div>
                        )}
                      </div>

                      <div
                        className={`border rounded-md p-4 flex items-center gap-3 cursor-pointer ${
                          formData.type === "python"
                            ? "border-primary bg-primary/5"
                            : "border-border hover:border-primary/50"
                        }`}
                        onClick={() => handleSelectChange("type", "python")}
                      >
                        <div className="w-8 h-8 flex items-center justify-center">
                          <img src="/assets/python.svg" alt="Python" width={28} height={28} />
                        </div>
                        <div className="flex-1">Python</div>
                        {formData.type === "python" && (
                          <div className="w-5 h-5 rounded-full border-2 border-primary flex items-center justify-center">
                            <div className="w-2.5 h-2.5 rounded-full bg-primary"></div>
                          </div>
                        )}
                      </div>
                    </div>
                    {errors.type && <p className="text-xs text-red-500">{errors.type}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label>
                      发布方式 <span className="text-red-500">*</span>
                    </Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div
                        className={`border rounded-md p-3 flex items-center gap-3 cursor-pointer ${
                          formData.publishType === "npm"
                            ? "border-primary bg-primary/5"
                            : "border-border hover:border-primary/50"
                        }`}
                        onClick={() => handleSelectChange("publishType", "npm")}
                      >
                        <div className="w-5 h-5 rounded-full border border-gray-300 flex items-center justify-center">
                          {formData.publishType === "npm" && <div className="w-3 h-3 rounded-full bg-primary"></div>}
                        </div>
                        <div className="flex-1 text-sm">
                          我已发布到{formData.type === "python" ? "PyPI" : "npm"}仓库
                        </div>
                      </div>

                      <div
                        className={`border rounded-md p-3 flex items-center gap-3 cursor-pointer ${
                          formData.publishType === "github"
                            ? "border-primary bg-primary/5"
                            : "border-border hover:border-primary/50"
                        }`}
                        onClick={() => handleSelectChange("publishType", "github")}
                      >
                        <div className="w-5 h-5 rounded-full border border-gray-300 flex items-center justify-center">
                          {formData.publishType === "github" && <div className="w-3 h-3 rounded-full bg-primary"></div>}
                        </div>
                        <div className="flex-1 text-sm">我已提交代码到GitHub/Gitee</div>
                      </div>
                    </div>
                  </div>

                  {formData.publishType === "npm" && (
                    <div className="space-y-2">
                      <Label htmlFor="package">
                        包名 <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="package"
                        name="package"
                        value={formData.package}
                        onChange={handleChange}
                        placeholder={formData.type === "python" ? "例如: my-python-package" : "例如: wonderwhy-er/111"}
                        className={errors.package ? "border-red-500" : ""}
                      />
                      {errors.package && <p className="text-xs text-red-500">{errors.package}</p>}
                    </div>
                  )}

                  {formData.publishType === "github" && (
                    <div className="space-y-2">
                      <Label htmlFor="github">
                        项目代码链接 <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="github"
                        name="github"
                        value={formData.github}
                        onChange={handleChange}
                        placeholder="例如: https://github.com/username/repo"
                        className={errors.github ? "border-red-500" : ""}
                      />
                      {errors.github && <p className="text-xs text-red-500">{errors.github}</p>}
                    </div>
                  )}
                </div>
              </form>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => router.push("/")}>
                取消
              </Button>
              <Button className="bg-primary hover:bg-primary/90" onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? (
                  <span className="flex items-center gap-2">
                    <Upload theme="outline" size="16" className="animate-spin" />
                    提交中...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <Upload theme="outline" size="16" />
                    提交
                  </span>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
      <SiteFooter />
    </>
  )
}
