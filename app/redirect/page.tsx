"use client"

import { useEffect } from "react"
export default function ApiKeysPage() {
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    if (code) {
      console.log(code, "code")
      window.parent.postMessage({ wxCode: code, wxState: state }, "*");
    }
  }, [])

  return (
    <div style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', backgroundColor: '#fff' }}></div>
  )
}
