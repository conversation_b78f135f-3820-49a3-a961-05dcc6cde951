import projectsApi from "@/lib/api/apis/projects"
import ServerDetailClient from "./ServerDetailClient"

// 强制动态渲染 - 确保每次请求时都重新获取数据
export const dynamic = 'force-dynamic'
export const revalidate = 0

// SSR 方式获取数据
async function getServerDetail(id: string) {
  try {
    const response: any = await projectsApi.findProjectByUUID(id)
    console.log(response, "responseresponse")
    if (response.code === 0 && response.data) {
      return {
        ...response.data,
        tags: response.data.tags ? response.data.tags.split(",") : []
      }
    }
    return null
  } catch (error) {
    console.error('Error fetching server detail:', error)
    return null
  }
}

// SSR 方式获取相关服务器
async function getRelatedServers(category: string, excludeId: number) {
  try {
    const params = {
      page: 1,
      pageSize: 4,
      category: category || "",
      orderBy: "random",
    }
    const response: any = await projectsApi.getProjectsList(params)
    if (response && response.data) {
      let arr = response.data.list.filter((item: any) => item.ID !== excludeId);
      return arr.splice(0, 3)
    }
    return []
  } catch (error) {
    console.error('Error fetching related servers:', error)
    return []
  }
}

export default async function ServerDetailPage({ params }: { params: { id: string } }) {
  const { id } = await params;
  const server = await getServerDetail(id)
  const relatedServers = server ? await getRelatedServers(server.category, server.ID) : []
  
  return <ServerDetailClient server={server} relatedServers={relatedServers} serverId={id} />
}
