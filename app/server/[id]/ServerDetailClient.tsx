"use client"

import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useState, useEffect, use<PERSON>allback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Left, Calendar, Computer, Earth, Like, AppleOne, Windows, Copy } from "@icon-park/react"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { useAuthGuard } from "@/hooks/use-auth-guard"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AiSuggestionDialog } from "./components/ai-suggestion-dialog"
import { ServerIntroTab } from "./components/server-intro-tab"
import { ServerToolsTab } from "./components/server-tools-tab"
import { ServerCasesTab } from "./components/server-cases-tab"
import { RelatedServers } from "./components/related-servers"
import projectsApi from "@/lib/api/apis/projects"
import { toast } from 'sonner';
import { useMcpClient } from '@/hooks/useMcpClient';
type Tool = {
  descriptionChinese: string
  name: string
  description: string
  points: number
  c_name: string
  inputSchema: {
    type: string
    properties: Record<string, any>
    required?: string[]
  }
}

type Server = {
  callType: number
  baseUrl: any
  isLiked: any
  category: string
  ID: number
  id: string
  name: string
  description: string
  descriptionChinese: string
  tags: string[]
  logoUrl: string
  callMethod: string[]
  package: string
  exampleConfig: string
  likes: number
  UpdatedAt: string
  offlineUsageCount: number
  onlineUsageCount: number
  detail: string
  github: string
  providerUrl: string
  projectTools: Tool[]
  command: string,
  args: string[],
  env: any,
  questions: string,
  streamableHttpUrl: string,
  sseUrl: string,
}

export default function ServerDetailClient({ 
  server: initialServer, 
  relatedServers, 
  serverId 
}: { 
  server: Server | null, 
  relatedServers: any[], 
  serverId: string 
}) {
  const router = useRouter()
  const [showInstallConfig, setShowInstallConfig] = useState(false)
  const [showApiUrl, setShowApiUrl] = useState(false)
  const { isAuthenticated, user, isLoading, loginDialogOpen, setLoginDialogOpen } = useAuth()
  const {
    requireAuth,
  } = useAuthGuard()
  
  // Add a reference to track if we've shown the API URL after login
  const [hasLoggedInRecently, setHasLoggedInRecently] = useState(false)
  const [copied, setCopied] = useState(false)
  const [selectedOS, setSelectedOS] = useState<"mac" | "windows">("mac")
  const [selectedMcpType, setSelectedMcpType] = useState<"sse" | "streamablehttp">("sse")
  const [likeAnimating, setLikeAnimating] = useState(false)
  const [server, setServer] = useState<Server | null>(initialServer)
  const [recommendServers, setRecommendServers] = useState<any[]>(relatedServers)
  const [macConfig, setMacConfig] = useState<any>(null)
  const [windowsConfig, setWindowsConfig] = useState<any>(null)
  const [sseUrl, setSseUrl] = useState<string>("")
  const [streamableHttpUrl, setStreamableHttpUrl] = useState<string>("")
  // 在组件顶层调用 useMcpClient Hook
  const mcpClient = server?.callMethod.includes("online")? useMcpClient(streamableHttpUrl? streamableHttpUrl : ''): { client: null, error: null, isConnected: false };
  const formatJson = (jsonString: string) => {
    try {
      const obj = JSON.parse(jsonString);
      return JSON.stringify(obj, null, 2);
    } catch (e) {
      return jsonString;
    }
  }

  const handleLoginSuccess = useCallback(() => {
    setShowApiUrl(true)
  }, [])

  useEffect(() => {
    if(user && server) {
      projectsApi.getIsLiked(server.ID).then((res: any) => {
        if(res.code === 0) {
          if(res.data.sseUrl) {
            setSseUrl(relocatePathSegment(location.origin + res.data.sseUrl))
          }
          if(res.data.streamableHttpUrl) {
            setStreamableHttpUrl(relocatePathSegment(location.origin + res.data.streamableHttpUrl))
          }
          setServer({
            ...server,
            ...res.data
          })
        }
      })
    }
  }, [user])

  useEffect(() => {
    if (server) {
      setMacConfig(JSON.stringify({
        "mcpServers": {
          [server.name]: {
            "command": server.command,
            "args": server.args || [],
            "env": server.env || {}
          }
        }
      }))
      setWindowsConfig(JSON.stringify({
        "mcpServers": {
          [server.name]: {
            "command": "cmd",
            "args": [
              "/c",
              server.command,
              ...(server.args || [])
            ],
            "env": server.env || {}
          }
        }
      }))
    }
  }, [server])

  // 在server变化时打印MCP客户端连接状态
  useEffect(() => {
    if (server?.baseUrl && mcpClient) {
      // console.log("MCP客户端已连接:", mcpClient)
    }
  }, [server, mcpClient])

  // Handle login success
  useEffect(() => {
    // When login dialog opens, mark that we're in the login process
    if (loginDialogOpen) {
      setHasLoggedInRecently(true)
    }
  }, [loginDialogOpen])

  useEffect(() => {
    // If user is authenticated and we just completed the login process
    if (isAuthenticated && hasLoggedInRecently) {
      // Close the dialog and show the API URL
      setLoginDialogOpen(false)
      handleLoginSuccess()
      // Reset the flag
      setHasLoggedInRecently(false)
    }
  }, [isAuthenticated, hasLoggedInRecently, handleLoginSuccess])
  const relocatePathSegment = (url: string) => {
    if (!url.includes('?')) return url;
  
    const [base, query] = url.split('?');
  
    const slashIndex = query.indexOf('/');
    if (slashIndex === -1) return url; // 没有 /，不处理
  
    // 提取要插入的路径段
    const pathPart = query.slice(slashIndex).split(/[&#]/)[0];
  
    // 删除 query 中的路径段（只删第一个匹配）
    const cleanedQuery = query.replace(pathPart, '');
  
    // 拼接新的 URL
    return `${base}${pathPart}?${cleanedQuery}`;
  }
  
  // 如果找不到服务器，显示错误信息
  if (!server) {
    return (
      <>
        <SiteHeader />
        <div className="container mx-auto py-12 px-4 text-center gradient-bg min-h-screen">
          <h1 className="text-2xl font-bold mb-4">服务器未找到</h1>
          <p className="mb-6">无法找到ID为 {serverId} 的服务器</p>
          <Button onClick={() => router.push("/")} className="bg-primary ">
            <Left theme="outline" size="16" className="mr-2" />
            返回服务器列表
          </Button>
        </div>
        <SiteFooter />
      </>
    )
  }

  // 检查服务器是否支持在线调用
  const supportsOnlineCall = server.callMethod.includes("online")

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => {
      setCopied(false)
    }, 2000)
  }

  const handleLike = async () => {
    if (!isAuthenticated) {
      setLoginDialogOpen(true)
      return;
    }
    let res: any;
    if(server.isLiked) {
      res = await projectsApi.unlikeProject(server.ID)
      toast.success("取消点赞成功")
    } else {
      res = await projectsApi.likeProject(server.ID)
      toast.success("点赞成功")
    }
    if(res.code === 0) {
      setServer({
        ...server,
        isLiked: !server.isLiked,
        likes: !server.isLiked ? server.likes + 1 : server.likes - 1
      })
      setLikeAnimating(true)
      setTimeout(() => {
        setLikeAnimating(false)
      }, 300)
    }
  }

  const handleLocalInstall = async () => {
    setShowInstallConfig(true)
    let res: any = await projectsApi.incrementOfflineUsageCount(server.ID);
    if(res.code === 0) {
      setServer({
        ...server,
        offlineUsageCount: server.offlineUsageCount + 1
      })
    }
  }
  
  const handleCopy = () => {
    let text: string;
    if(showApiUrl) {
      if(selectedMcpType === "sse") {
        text = sseUrl
      } else {
        text = streamableHttpUrl
      }
    } else {
      if(selectedOS === "mac") {
        text = macConfig
      } else {
        text = windowsConfig
      }
    }
    copyToClipboard(text)
  }

  return (
    <>
      <style jsx global>{`
        .custom-tooltip {
          visibility: hidden;
          opacity: 0;
          transition: opacity 0.2s, visibility 0.2s;
          transition-delay: 0s;
        }
        
        .tooltip-trigger:hover .custom-tooltip {
          visibility: visible;
          opacity: 1;
          transition-delay: 0.5s; /* 500ms delay before showing tooltip */
        }
      `}</style>
      <SiteHeader />
      <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
        <div className="mb-6">
          <Link href="/" className="inline-flex items-center text-muted-foreground hover:text-primary">
            <Left theme="outline" size="16" className="mr-1" />
            <span>返回服务器列表</span>
          </Link>
        </div>

        <div className="space-y-6">
          {/* Combined Server Header and Install Config Card */}
          <Card className="relative border-t-4 border-t-primary/70">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-6">
                {/* Server Info Section */}
                <div className="flex-1">
                  <div className="flex items-start gap-3 mb-4">
                    <div className="w-12 h-12 rounded-md overflow-hidden flex-shrink-0">
                      <img
                        src={server.logoUrl || "/placeholder.svg"}
                        alt={`${server.name} logo`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h1 className="text-2xl font-bold text-primary flex items-center gap-2">
                        {server.name}
                        {/* 点赞按钮 */}
                        <button
                          onClick={handleLike}
                          className="flex items-center gap-1 text-muted-foreground hover:text-red-500 transition-colors ml-2"
                        >
                          <Like
                            theme={server.isLiked ? "filled" : "outline"}
                            size="16"
                            className={`
                              ${server.isLiked ? "text-red-500" : ""} 
                              ${likeAnimating ? "scale-125" : "scale-100"} 
                              transition-all duration-300
                            `}
                          />
                          <span className="font-medium">{server.likes}</span>
                        </button>
                      </h1>
                      <p className="text-base text-muted-foreground mt-1" dangerouslySetInnerHTML={{ __html: server.descriptionChinese? server.descriptionChinese : server.description }}></p>
                    </div>
                  </div>
                  {
                    server.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {server.tags.map((tag) => (
                          <Link href={`/?tag=${encodeURIComponent(tag)}`} key={tag} className="no-underline">
                            <Badge
                              variant="outline"
                              className="cursor-pointer hover:bg-primary/10 hover:text-primary transition-colors"
                            >
                              {tag}
                            </Badge>
                          </Link>
                        ))}
                      </div>
                    )
                  }
                  

                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-1">
                      <Calendar theme="outline" size="16" />
                      <span>最后更新: {server.UpdatedAt}</span>
                    </div>
                  </div>

                  {/* Installation Counts */}
                  <div className="flex items-center gap-4 text-sm">
                    {server.callMethod.includes("local") && (
                      <div className="flex items-center gap-1 text-primary">
                        <Computer theme="outline" size="16" />
                        <span className="text-sm font-medium">{server.offlineUsageCount.toLocaleString()}</span>
                        <span className="text-muted-foreground">本地安装次数</span>
                      </div>
                    )}
                    {server.callMethod.includes("online") && (
                      <div className="flex items-center gap-1 text-primary">
                        <Earth theme="outline" size="16" />
                        <span className="text-sm font-medium">{server.onlineUsageCount.toLocaleString()}</span>
                        <span className="text-muted-foreground">在线调用次数</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Installation Section */}
                <div className="lg:w-1/3 lg:border-l lg:pl-6">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-semibold text-secondary"> <div>{ showInstallConfig? '本地安装' : showApiUrl? '在线调用' : '客户端调用方式' }</div> </h2>
                    {(showInstallConfig || showApiUrl) && (
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => {
                            setShowInstallConfig(false)
                            setShowApiUrl(false)
                          }}
                          className="text-muted-foreground hover:text-primary flex items-center"
                        >
                          <Left theme="outline" size="16" className="mr-1" />
                          <span>返回</span>
                        </button>
                        {(showInstallConfig || showApiUrl) && (
                          <button
                            className={`text-muted-foreground hover:text-primary transition-all duration-200 flex items-center ${copied ? "text-green-500" : ""}`}
                            onClick={handleCopy}
                          >
                            <Copy theme="outline" size="16" className="mr-1" />
                            <span>{copied ? "已复制" : "复制"}</span>
                          </button>
                        )}
                      </div>
                    )}
                  </div>

                  {!showInstallConfig && !showApiUrl ? (
                    <div className="space-y-4">
                      {server.callMethod.includes("local") && (
                        <Button
                          variant="outline"
                          className="w-full border-2 border-secondary/50 hover:border-secondary hover:bg-secondary/10 hover:text-secondary focus:ring-2 focus:ring-secondary/50 active:bg-secondary/20 h-12 text-base font-medium transition-all duration-200"
                          onClick={handleLocalInstall}
                        >
                          <Computer theme="outline" size="18" className="mr-2" />
                          本地安装
                        </Button>
                      )}
                      {server.callMethod.includes("online") && (
                        <Button
                          variant="outline"
                          className="w-full border-2 border-secondary/50 hover:border-secondary hover:bg-secondary/10 hover:text-secondary focus:ring-2 focus:ring-secondary/50 active:bg-secondary/20 h-12 text-base font-medium transition-all duration-200"
                          onClick={() => {
                            if (isAuthenticated) {
                              setShowApiUrl(true)
                            } else {
                              setLoginDialogOpen(true)
                            }
                          }}
                        >
                          <Earth theme="outline" size="18" className="mr-2" />
                          在线调用
                        </Button>
                      )}
                      {server.callMethod.length === 0 && (
                        <div className="text-center py-4 text-muted-foreground">暂无可用的调用方式</div>
                      )}
                    </div>
                  ) : showApiUrl ? (
                    <div>
                      <Tabs
                        defaultValue={selectedMcpType}
                        onValueChange={(value) => setSelectedMcpType(value as "sse" | "streamablehttp")}
                        className="w-full"
                      >
                        <TabsList className="w-full mb-4 bg-muted border border-border p-1">
                          {
                            sseUrl &&
                            <TabsTrigger
                              value="sse"
                              className="flex-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                            >
                              <div className="flex items-center gap-2">
                                <span>SSE</span>
                              </div>
                            </TabsTrigger>
                          }
                          {
                            streamableHttpUrl && 
                            <TabsTrigger
                              value="streamablehttp"
                              className="flex-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                            >
                              <div className="flex items-center gap-2">
                                <span>StreamableHTTP</span>
                              </div>
                            </TabsTrigger>
                          }
                          
                        </TabsList>
                        
                        <TabsContent value="sse" className="mt-0">
                          <div className="relative rounded-md overflow-hidden">
                            <pre className="bg-muted/30 p-4 rounded-md text-sm font-mono break-all whitespace-pre-wrap overflow-x-auto max-w-full">
                              {sseUrl}
                            </pre>
                          </div>
                        </TabsContent>
                        
                        <TabsContent value="streamablehttp" className="mt-0">
                          <div className="relative rounded-md overflow-hidden">
                            <pre className="bg-muted/30 p-4 rounded-md text-sm font-mono break-all whitespace-pre-wrap overflow-x-auto max-w-full">
                              {streamableHttpUrl}
                            </pre>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  ) : (
                    <div>
                      <Tabs
                        defaultValue={selectedOS}
                        onValueChange={(value) => setSelectedOS(value as "mac" | "windows")}
                        className="w-full"
                      >
                        <TabsList className="w-full mb-4 bg-muted border border-border p-1">
                          <TabsTrigger
                            value="mac"
                            className="flex-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                          >
                            <div className="flex items-center gap-2">
                              <AppleOne theme="outline" size="18" />
                              <span>Mac/Linux</span>
                            </div>
                          </TabsTrigger>
                          <TabsTrigger
                            value="windows"
                            className="flex-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                          >
                            <div className="flex items-center gap-2">
                              <Windows theme="outline" size="18" />
                              <span>Windows</span>
                            </div>
                          </TabsTrigger>
                        </TabsList>
                        <div className="relative rounded-md overflow-hidden">
                          <pre className="bg-muted/30 p-4 rounded-md text-sm font-mono break-all whitespace-pre-wrap overflow-x-auto max-w-full">
                            {formatJson(selectedOS === "mac" ? macConfig : windowsConfig)}
                          </pre>
                        </div>
                      </Tabs>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
          {
            /* 检查是否有右侧内容 */
            (recommendServers.length || supportsOnlineCall)? (
            <div className="flex flex-col lg:flex-row gap-6">
              <div className="flex-1 lg:w-2/3 space-y-6">
                <Tabs defaultValue="intro" className="[&>div[role=tabpanel]]:mt-0">
                  <div className="mb-0 flex justify-start">
                    <TabsList className="flex rounded-none p-0 bg-transparent h-auto">
                      <TabsTrigger
                        value="intro"
                        className="px-10 py-2 rounded-tl-md rounded-tr-md rounded-bl-none rounded-br-none border border-b-0 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-primary data-[state=active]:rounded-bl-none data-[state=active]:rounded-br-none data-[state=inactive]:bg-muted/30 data-[state=inactive]:border-border hover:rounded-bl-none hover:rounded-br-none data-[state=inactive]:hover:bg-secondary/10"
                      >
                        介绍
                      </TabsTrigger>
                      <TabsTrigger
                        value="tools"
                        className="px-10 py-2 rounded-tl-md rounded-tr-md rounded-bl-none rounded-br-none border border-b-0 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-primary data-[state=active]:rounded-bl-none data-[state=active]:rounded-br-none data-[state=inactive]:bg-muted/30 data-[state=inactive]:border-border hover:rounded-bl-none hover:rounded-br-none data-[state=inactive]:hover:bg-secondary/10"
                      >
                        工具
                      </TabsTrigger>
                      <TabsTrigger
                        value="cases"
                        className="px-10 py-2 rounded-tl-md rounded-tr-md rounded-bl-none rounded-br-none border border-b-0 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-primary data-[state=active]:rounded-bl-none data-[state=active]:rounded-br-none data-[state=inactive]:bg-muted/30 data-[state=inactive]:border-border hover:rounded-bl-none hover:rounded-br-none data-[state=inactive]:hover:bg-secondary/10"
                      >
                        案例
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <TabsContent value="intro" className="mt-0">
                    <ServerIntroTab detail={server.detail} github={server.github} website={server.providerUrl} />
                  </TabsContent>

                  <TabsContent value="tools" className="mt-0">
                    <ServerToolsTab server={server} isAuthenticated={isAuthenticated} tools={server.projectTools} requireAuth={requireAuth} client={mcpClient.client} isConnected={mcpClient.isConnected} setLoginDialogOpen={setLoginDialogOpen} />
                  </TabsContent>

                  <TabsContent value="cases" className="mt-0">
                    <ServerCasesTab serverId={server.ID} />
                  </TabsContent>
                </Tabs>
                <div className="flex justify-center">
                  <a href={`aido://server?id=${serverId}`} target="_blank" className="inline-flex items-center text-muted-foreground hover:text-primary">
                    <span style={{
                      color: "rgba(255,255,255,0)",
                      cursor: "default",
                      userSelect: "none",
                    }}>试测</span>
                  </a>
                </div>
              </div>

              <div className="lg:w-1/3 space-y-6">
                {/* 只有当服务器支持在线调用时才显示"在线试一试"组件 */}
                {supportsOnlineCall && (server.callType === 2 || server.callType === 3) && <AiSuggestionDialog questions={server.questions ? JSON.parse(server.questions) : []} client={mcpClient.client} isConnected={mcpClient.isConnected} isAuthenticated={isAuthenticated} setLoginDialogOpen={setLoginDialogOpen} />}
                {recommendServers.length > 0 && <RelatedServers servers={recommendServers} />}
              </div>
            </div>
          ) : (
            <div className="w-full">
              <Tabs defaultValue="intro" className="w-full [&>div[role=tabpanel]]:mt-0">
                <div className="mb-0 flex justify-start">
                  <TabsList className="flex rounded-none p-0 bg-transparent h-auto">
                    <TabsTrigger
                      value="intro"
                      className="px-10 py-2 rounded-tl-md rounded-tr-md rounded-bl-none rounded-br-none border border-b-0 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-primary data-[state=active]:rounded-bl-none data-[state=active]:rounded-br-none data-[state=inactive]:bg-muted/30 data-[state=inactive]:border-border hover:rounded-bl-none hover:rounded-br-none data-[state=inactive]:hover:bg-secondary/10"
                    >
                      介绍
                    </TabsTrigger>
                    <TabsTrigger
                      value="tools"
                      className="px-10 py-2 rounded-tl-md rounded-tr-md rounded-bl-none rounded-br-none border border-b-0 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-primary data-[state=active]:rounded-bl-none data-[state=active]:rounded-br-none data-[state=inactive]:bg-muted/30 data-[state=inactive]:border-border hover:rounded-bl-none hover:rounded-br-none data-[state=inactive]:hover:bg-secondary/10"
                    >
                      工具
                    </TabsTrigger>
                    <TabsTrigger
                      value="cases"
                      className="px-10 py-2 rounded-tl-md rounded-tr-md rounded-bl-none rounded-br-none border border-b-0 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-primary data-[state=active]:rounded-bl-none data-[state=active]:rounded-br-none data-[state=inactive]:bg-muted/30 data-[state=inactive]:border-border hover:rounded-bl-none hover:rounded-br-none data-[state=inactive]:hover:bg-secondary/10"
                    >
                      案例
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="intro" className="mt-0">
                  <ServerIntroTab detail={server.detail} github={server.github} website={server.providerUrl} />
                </TabsContent>

                <TabsContent value="tools" className="mt-0">
                  <ServerToolsTab server={server} isAuthenticated={isAuthenticated} tools={server.projectTools} requireAuth={requireAuth} client={mcpClient.client} isConnected={mcpClient.isConnected} setLoginDialogOpen={setLoginDialogOpen} />
                </TabsContent>

                <TabsContent value="cases" className="mt-0">
                  <ServerCasesTab serverId={server.ID} />
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </div>
      <SiteFooter />
    </>
  )
} 