"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { PlayOne } from "@icon-park/react"
import { useState } from "react"
import { Card } from "@/components/ui/card"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

type Tool = {
  name: string
  description: string
  points: number
  inputSchema: {
    type: string
    properties: Record<string, any>
    required?: string[]
  }
}

type ToolDetailProps = {
  tool: Tool
  toolParams: Record<string, any>
  setToolParams: (params: Record<string, any>) => void
  paramErrors: Record<string, string>
  setParamErrors: (errors: Record<string, string>) => void
  requireAuth: (callback: () => void) => void
  client?: any // MCP客户端
  isConnected?: boolean // 添加连接状态属性
}

export function ToolDetail({
  tool,
  toolParams,
  setToolParams,
  paramErrors,
  setParamErrors,
  requireAuth,
  client,
  isConnected = false
}: ToolDetailProps) {
  const [toolResult, setToolResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 更新参数值
  const handleParamChange = (key: string, value: any) => {
    setToolParams({
      ...toolParams,
      [key]: value,
    })

    // 清除该字段的错误
    if (paramErrors[key]) {
      const newErrors = { ...paramErrors }
      delete newErrors[key]
      setParamErrors(newErrors)
    }
  }

  // 处理数组类型参数的复选框变化
  const handleArrayParamChange = (key: string, value: string, checked: boolean) => {
    const currentArray = Array.isArray(toolParams[key]) ? [...toolParams[key]] : []
    const newArray = checked ? [...currentArray, value] : currentArray.filter((item) => item !== value)

    // 如果数组不为空，清除该字段的错误
    if (newArray.length > 0 && paramErrors[key]) {
      const newErrors = { ...paramErrors }
      delete newErrors[key]
      setParamErrors(newErrors)
    }

    setToolParams({
      ...toolParams,
      [key]: newArray,
    })
  }

  // 修改运行工具函数，添加积分检查和消耗逻辑
  const handleRunTool = () => {
    requireAuth(() => {
      // 首先验证必填参数
      let isValid = true
      const newErrors: Record<string, string> = {}

      if (tool?.inputSchema.required && tool.inputSchema.required.length > 0) {
        tool.inputSchema.required.forEach((param) => {
          // 检查参数是否为空（考虑不同类型的空值）
          const value = toolParams[param]
          if (value === undefined || value === null || value === "") {
            newErrors[param] = "此字段为必填项"
            isValid = false
          } else if (Array.isArray(value) && value.length === 0) {
            newErrors[param] = "请至少选择一项"
            isValid = false
          } else if (typeof value === "object" && Object.keys(value).length === 0) {
            newErrors[param] = "请填写有效的内容"
            isValid = false
          }
        })
      }

      setParamErrors(newErrors)

      if (!isValid) {
        return
      }

      // 检查客户端是否存在
      if (!client) {
        setError("MCP客户端未连接")
        return
      }

      // 设置加载状态，清除之前的结果和错误
      setIsLoading(true)
      setToolResult(null)
      setError(null)

      // 调用工具
      callTool(tool.name, toolParams)
    })
  }

  // 调用MCP工具
  const callTool = async (toolName: string, args: Record<string, any>) => {
    try {
      console.log('调用工具:', { name: toolName, args })
      const response = await client.callTool(toolName, args)
      console.log('工具返回结果:', response)
      setToolResult(response)
      
      // 如果工具有积分消耗，可以在这里显示
      if (tool.points > 0) {
        console.log("消耗积分:", tool.points)
      }
      
      // 增加在线调用次数（这里通常应该由后端自动处理）
      try {
        const projectId = window.location.search.match(/id=([^&]+)/)?.[1]
        if (projectId) {
          // 注意：理想情况下应该有一个专门的API来增加在线调用次数
          // 由于API不存在，这里记录日志，实际项目中应添加相应API
          console.log('工具在线调用成功，项目ID:', projectId)
          
          // 如果后端添加了相应API，可以取消下面注释的代码
          // import('@/lib/api/apis/projects').then(projectsApi => {
          //   projectsApi.default.incrementOnlineUsageCount(parseInt(projectId))
          //   console.log('增加在线调用次数成功')
          // })
        }
      } catch (err) {
        console.error('记录工具调用失败:', err)
      }
    } catch (err) {
      console.error('工具调用错误:', err)
      setError(err instanceof Error ? err.message : '工具调用失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <h4 className="font-medium">参数设置</h4>
      <div className="space-y-4">
        {tool.inputSchema.properties && Object.keys(tool.inputSchema.properties).map((key) =>
          renderParamInput(
            key,
            tool.inputSchema.properties[key],
            toolParams,
            handleParamChange,
            handleArrayParamChange,
            tool.inputSchema.required?.includes(key) || false,
            paramErrors[key],
          ),
        )}
      </div>

      <Button 
        className="w-full mt-4 bg-primary hover:bg-primary/90" 
        onClick={handleRunTool}
        disabled={isLoading || !isConnected}
        title={!isConnected ? "连接失败" : ""}
      >
        <PlayOne theme="outline" size="18" className="mr-2" />
        {isLoading ? '运行中...' : !isConnected ? '连接失败' : '在线运行'}
      </Button>

      {!isConnected && (
        <Alert variant="destructive" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>MCP连接失败，无法执行工具。请检查连接状态。</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {toolResult && (
        <div className="mt-4">
          <h4 className="font-medium mb-2">执行结果</h4>
          <Card className="p-4 bg-muted/50">
            {typeof toolResult === 'string' ? (
              <pre className="whitespace-pre-wrap text-sm overflow-auto max-h-96">
                {toolResult}
              </pre>
            ) : (
              <div className="json-viewer overflow-auto max-h-[35rem]">
                <JsonViewer data={toolResult} />
              </div>
            )}
          </Card>
        </div>
      )}
    </div>
  )
}

// 渲染参数输入控件
function renderParamInput(
  key: string,
  schema: any,
  toolParams: Record<string, any>,
  handleParamChange: (key: string, value: any) => void,
  handleArrayParamChange: (key: string, value: string, checked: boolean) => void,
  isRequired: boolean,
  error?: string,
) {
  const label = `${schema.description || key}${isRequired ? " *" : ""}`
  const hasError = !!error

  if (schema.type === "string") {
    if (schema.enum) {
      return (
        <div className="space-y-2" key={key}>
          <Label htmlFor={key}>{label}</Label>
          <Select value={toolParams[key] || ""} onValueChange={(value) => handleParamChange(key, value)}>
            <SelectTrigger id={key} className={hasError ? "border-red-500" : ""}>
              <SelectValue placeholder="请选择" />
            </SelectTrigger>
            <SelectContent>
              {schema.enum.map((option: string) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {hasError && <p className="text-xs text-red-500">{error}</p>}
        </div>
      )
    } else {
      return (
        <div className="space-y-2" key={key}>
          <Label htmlFor={key}>{label}</Label>
          <Input
            id={key}
            value={toolParams[key] || ""}
            onChange={(e) => handleParamChange(key, e.target.value)}
            placeholder={schema.description || key}
            className={hasError ? "border-red-500" : ""}
          />
          {hasError && <p className="text-xs text-red-500">{error}</p>}
        </div>
      )
    }
  } else if (schema.type === "number" || schema.type === "integer") {
    return (
      <div className="space-y-2" key={key}>
        <Label htmlFor={key}>{label}</Label>
        <Input
          id={key}
          type="number"
          value={toolParams[key] || ""}
          onChange={(e) => handleParamChange(key, e.target.value ? Number(e.target.value) : "")}
          placeholder={schema.description || key}
          min={schema.minimum}
          max={schema.maximum}
          className={hasError ? "border-red-500" : ""}
        />
        {hasError && <p className="text-xs text-red-500">{error}</p>}
      </div>
    )
  } else if (schema.type === "array" && schema.items && schema.items.enum) {
    return (
      <div className="space-y-2" key={key}>
        <Label>{label}</Label>
        <div className={`space-y-2 border rounded-md p-3 ${hasError ? "border-red-500" : ""}`}>
          {schema.items.enum.map((option: string) => (
            <div className="flex items-center space-x-2" key={option}>
              <Checkbox
                id={`${key}-${option}`}
                checked={Array.isArray(toolParams[key]) && toolParams[key].includes(option)}
                onCheckedChange={(checked) => handleArrayParamChange(key, option, checked === true)}
              />
              <Label htmlFor={`${key}-${option}`} className="cursor-pointer">
                {option}
              </Label>
            </div>
          ))}
        </div>
        {hasError && <p className="text-xs text-red-500">{error}</p>}
      </div>
    )
  } else if (schema.type === "array" && schema.items && schema.items.type === "string") {
    // 处理字符串数组类型
    return (
      <div className="space-y-2" key={key}>
        <Label>{label}</Label>
        <div className={`space-y-2 border rounded-md p-3 ${hasError ? "border-red-500" : ""}`}>
          {Array.isArray(toolParams[key]) &&
            toolParams[key].map((item: string, index: number) => (
              <div className="flex items-center gap-2" key={index}>
                <Input
                  value={item}
                  onChange={(e) => {
                    const newArray = [...toolParams[key]]
                    newArray[index] = e.target.value
                    handleParamChange(key, newArray)
                  }}
                  placeholder={`参数 ${index + 1}`}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 text-red-500 hover:text-red-600 hover:bg-red-50"
                  onClick={() => {
                    const newArray = [...toolParams[key]]
                    newArray.splice(index, 1)
                    handleParamChange(key, newArray)
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </Button>
              </div>
            ))}

          <Button
            type="button"
            variant="outline"
            className="w-full mt-2"
            onClick={() => {
              const currentArray = Array.isArray(toolParams[key]) ? [...toolParams[key]] : []
              handleParamChange(key, [...currentArray, ""])
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2"
            >
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            添加参数
          </Button>
        </div>
        {hasError && <p className="text-xs text-red-500">{error}</p>}
      </div>
    )
  } else if (schema.type === "object") {
    return (
      <div className="space-y-2" key={key}>
        <Label htmlFor={key}>{label}</Label>
        <Textarea
          id={key}
          value={toolParams[key] ? JSON.stringify(toolParams[key], null, 2) : ""}
          onChange={(e) => {
            try {
              const value = e.target.value ? JSON.parse(e.target.value) : {}
              handleParamChange(key, value)
            } catch (error) {
              // 如果不是有效的JSON，只更新文本
              handleParamChange(key, e.target.value)
            }
          }}
          placeholder={`请输入JSON格式的${schema.description || key}`}
          className={`font-mono ${hasError ? "border-red-500" : ""}`}
        />
        {hasError && <p className="text-xs text-red-500">{error}</p>}
      </div>
    )
  }

  return null
}

// 添加JSON查看器组件
function JsonViewer({ data }: { data: any }) {
  // 递归渲染JSON值
  const renderJsonValue = (value: any, level = 0, key?: string) => {
    if (value === null) {
      return <span className="text-destructive">null</span>;
    }

    if (typeof value === "boolean") {
      return <span className="text-secondary">{value.toString()}</span>;
    }

    if (typeof value === "number") {
      return <span className="text-accent">{value}</span>;
    }

    if (typeof value === "string") {
      // 检测是否为长文本内容
      if (value.length > 100 && key && (key === "text" || key === "itemDescription" || key.toLowerCase().includes("description"))) {
        return (
          <div className="text-primary whitespace-pre-wrap break-words pl-4 my-1 border-l-2 border-primary/30">
            {`"`}{value.split('\\n').map((line, i) => 
              line.replace(/\\r/g, '')
            ).join('\n')}{`"`}
          </div>
        );
      }
      
      // 检测URL或链接并添加点击功能
      if (value.match(/https?:\/\/[^\s"]+/)) {
        return (
          <span className="text-primary break-words">
            {`"`}
            <a 
              href={value} 
              target="_blank" 
              rel="noopener noreferrer"
              className="underline hover:text-primary/80"
            >
              {value}
            </a>
            {`"`}
          </span>
        );
      }
      return <span className="text-secondary break-words">{`"${value}"`}</span>;
    }

    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-gray-600">[]</span>;
      }
      return (
        <div>
          <span className="text-gray-600">[</span>
          {renderJsonArray(value, level)}
          <div style={{ marginLeft: `${(level - 1) * 8}px` }}>
            <span className="text-gray-600">]</span>
          </div>
        </div>
      );
    }

    if (typeof value === "object") {
      if (Object.keys(value).length === 0) {
        return <span className="text-gray-600">{"{}"}</span>;
      }
      return (
        <div>
          <span className="text-gray-600">{"{"}</span>
          {renderJsonObject(value, level)}
          <div style={{ marginLeft: `${(level - 1) * 8}px` }}>
            <span className="text-gray-600">{"}"}</span>
          </div>
        </div>
      );
    }

    return <span>{String(value)}</span>;
  };

  // 渲染JSON对象的键值对
  const renderJsonObject = (obj: any, level = 0) => {
    return Object.entries(obj).map(([key, value], index) => (
      <div key={`${key}-${index}`} className="pl-4" style={{ marginLeft: `${level * 8}px` }}>
        <span className="text-primary font-medium">{`"${key}"`}</span>
        <span className="text-muted-foreground">: </span>
        {renderJsonValue(value, level + 1, key)}
        {index < Object.entries(obj).length - 1 && <span className="text-muted-foreground">,</span>}
      </div>
    ));
  };

  // 渲染JSON数组
  const renderJsonArray = (arr: any[], level = 0) => {
    return arr.map((item, index) => (
      <div key={index} className="pl-4" style={{ marginLeft: `${level * 8}px` }}>
        {renderJsonValue(item, level + 1)}
        {index < arr.length - 1 && <span className="text-muted-foreground">,</span>}
      </div>
    ));
  };

  return (
    <div className="font-mono text-sm bg-muted/50 rounded-md p-2">
      {typeof data === "object" && data !== null ? (
        <div>
          {Array.isArray(data) ? (
            <div>
              <span className="text-muted-foreground">[</span>
              {renderJsonArray(data)}
              <span className="text-muted-foreground">]</span>
            </div>
          ) : (
            <div>
              <span className="text-muted-foreground">{"{"}</span>
              {renderJsonObject(data)}
              <span className="text-muted-foreground">{"}"}</span>
            </div>
          )}
        </div>
      ) : (
        <pre>{JSON.stringify(data, null, 2)}</pre>
      )}
    </div>
  );
}
