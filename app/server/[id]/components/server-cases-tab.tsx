"use client"

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LinkTwo } from "@icon-park/react"
import { useCasesData } from "@/lib/use-cases-data"
import Masonry from "react-masonry-css"
import useCaseApi from "@/lib/api/apis/useCase"
import { useEffect, useState } from "react"
type ServerCasesTabProps = {
  serverId: number
}

export function ServerCasesTab({ serverId }: ServerCasesTabProps) {
  // 筛选当前服务器的案例
  const [serverCases, setServerCases] = useState<any[]>([])
  const getList = async () => {
    const params = {
      page: 1,
      pageSize: 999,
      keyword: "",
      projectsId: serverId,
    }
    const response: any = await useCaseApi.getUseCasePublic(params)
    if (response && response.data) {
      setServerCases(response.data.list || [])
    }
  }
  useEffect(() => {
    getList()
  }, [])
  if (serverCases.length === 0) {
    return (
      <Card className="mb-6 relative border-t-0 rounded-tl-none">
        <CardContent className="pt-6">
          <div className="text-center py-6">
            <p className="text-muted-foreground">暂无相关案例</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 瀑布流断点配置
  const breakpointColumnsObj = {
    default: 2, // 默认2列，因为空间较小
    700: 1, // 在700px以下时显示1列
  }

  return (
    <Card className="mb-6 relative border-t-0 rounded-tl-none">
      <CardContent className="pt-6">
        <style jsx>{`
          .masonry-grid {
            display: flex;
            width: auto;
            margin-left: -16px; /* 调整间距 */
          }
          .masonry-grid-column {
            padding-left: 16px; /* 调整间距 */
            background-clip: padding-box;
          }
        `}</style>
        <Masonry breakpointCols={breakpointColumnsObj} className="masonry-grid" columnClassName="masonry-grid-column">
          {serverCases.map((useCase) => (
            <Card key={useCase.id} className="card-hover overflow-hidden mb-6">
              <CardHeader className="p-4 pb-0">
                <div className="flex items-start gap-3">
                  <Avatar className="h-12 w-12 border">
                    <AvatarImage src={useCase.avatar || "/placeholder.svg"} alt={useCase.author} />
                    <AvatarFallback>{useCase.author.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    {useCase.homepage ? (
                      <a
                        href={useCase.homepage}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="font-semibold hover:text-primary transition-colors"
                      >
                        {useCase.author}
                      </a>
                    ) : (
                      <span className="font-semibold">{useCase.author}</span>
                    )}
                    <div className="text-sm text-muted-foreground">{useCase.CreatedAt}</div>
                  </div>
                  {useCase.homepage && (
                    <a
                      href={useCase.homepage}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 text-xs text-muted-foreground hover:text-primary transition-colors"
                    >
                      <LinkTwo theme="outline" size="20" />
                      <span>主页</span>
                    </a>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-4 pb-6">
                <p className="text-sm pb-2 whitespace-pre-line">{useCase.summary}</p>
                {useCase.video ? (
                  <div className="rounded-lg overflow-hidden aspect-video">
                    <iframe
                      src={`${useCase.video.includes('?')? useCase.video + '&autoplay=0' : useCase.video + '?autoplay=0'}`}
                      className="w-full h-full"
                      title={`Video by ${useCase.author}`}
                      frameBorder="0"
                      allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    ></iframe>
                  </div>
                ) : useCase.cover ? (
                  <div className="rounded-lg overflow-hidden">
                    <img
                      src={useCase.cover || "/placeholder.svg"}
                      alt={`Cover image for ${useCase.author}'s post`}
                      className="w-full h-auto object-cover"
                    />
                  </div>
                ) : null}
              </CardContent>
            </Card>
          ))}
        </Masonry>
      </CardContent>
    </Card>
  )
}
