"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Return, PlayOne } from "@icon-park/react"
import { ToolDetail } from "./tool-detail"

type Tool = {
  c_name: string
  descriptionChinese: string
  name: string
  description: string
  points: number
  inputSchema: {
    type: string
    properties: Record<string, any>
    required?: string[]
  }
}
type Server = {
  callType: number
  callMethod: string[]
  baseUrl?: string
}
type ServerToolsTabProps = {
  server: Server
  tools: Tool[]
  requireAuth: (callback: () => void) => void
  client?: any // MCP客户端
  isConnected?: boolean // 添加连接状态属性
  isAuthenticated: boolean
  setLoginDialogOpen: (open: boolean) => void
}

export function ServerToolsTab({ server, tools, requireAuth, client, isConnected = false, isAuthenticated, setLoginDialogOpen }: ServerToolsTabProps) {
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null)
  const [toolParams, setToolParams] = useState<Record<string, any>>({})
  const [paramErrors, setParamErrors] = useState<Record<string, string>>({})

  // 选择工具
  const handleSelectTool = (tool: Tool) => {
    if(!isAuthenticated) {
      setLoginDialogOpen(true)
      return false;
    }
    setSelectedTool(tool)
    setParamErrors({}) // 清除之前的错误
    // 初始化参数
    const initialParams: Record<string, any> = {}
    if(tool.inputSchema.properties) {
      Object.keys(tool.inputSchema.properties).forEach((key) => {
        const prop = tool.inputSchema.properties[key]
        if (prop.type === "array") {
          initialParams[key] = []
        } else if (prop.type === "object") {
          initialParams[key] = {}
        } else if (prop.enum && prop.enum.length > 0) {
          initialParams[key] = prop.enum[0]
        } else {
          initialParams[key] = ""
        }
      })
      setToolParams(initialParams)
    }
    
  }

  // 返回工具列表
  const handleBackToTools = () => {
    setSelectedTool(null)
    setToolParams({})
  }

  return (
    <Card className="mb-6 relative border-t-0 rounded-tl-none">
      <CardContent className="pt-6">
        {selectedTool ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  onClick={handleBackToTools}
                  className="text-muted-foreground hover:text-primary flex items-center"
                >
                  <Return theme="outline" size="18" className="mr-1" />
                  <span>返回工具列表</span>
                </button>
              </div>
              {/* 工具详情头部美化 */}
              <div className="flex items-start justify-between gap-4 mb-2">
                <div>
                  <h3 className="text-xl font-bold leading-tight">{selectedTool.c_name}</h3>
                  <div className="text-sm text-primary/80 dark:text-primary/70 mt-1">{selectedTool.name}</div>
                </div>
                {selectedTool.points > 0 && (
                  <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 h-7 flex items-center">
                    {selectedTool.points} 积分
                  </Badge>
                )}
              </div>
            </div>
            <p className="text-muted-foreground">{selectedTool.description}</p>
            <Separator />

            <ToolDetail
              tool={selectedTool}
              toolParams={toolParams}
              setToolParams={setToolParams}
              paramErrors={paramErrors}
              setParamErrors={setParamErrors}
              requireAuth={requireAuth}
              client={client}
              isConnected={isConnected}
            />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {tools &&
                tools.map((tool, index) => (
                  <div key={index} className="border rounded-md p-4">
                    <div className="flex justify-between items-start gap-4 mb-3">
                      <div>
                        <h4 className="font-medium leading-tight">{tool.c_name}</h4>
                        <div className="text-sm text-primary/80 dark:text-primary/70 mt-0.5">{tool.name}</div>
                      </div>
                      {tool.points > 0 && (
                        <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 h-6 flex items-center">
                          {tool.points} 积分
                        </Badge>
                      )}
                    </div>
                    {
                      server.callMethod.includes("online") && (server.callType === 2 || server.callType === 3)? (
                      <>
                        <p className="text-sm text-muted-foreground mb-3">{tool.descriptionChinese || tool.description}</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full border-primary/30 hover:bg-primary/10 hover:text-primary"
                          onClick={() => handleSelectTool(tool)}
                          disabled={isAuthenticated && !isConnected}
                          title={!isConnected ? "MCP客户端未连接" : ""}
                        >
                          <PlayOne theme="outline" size="16" className="mr-1" />
                          {isAuthenticated && !isConnected ? "连接失败" : "在线运行"}
                        </Button>
                      </>)
                      :
                      <p className="text-sm text-muted-foreground mb-0">{tool.descriptionChinese || tool.description}</p>
                    }
                    
                  </div>
                ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
