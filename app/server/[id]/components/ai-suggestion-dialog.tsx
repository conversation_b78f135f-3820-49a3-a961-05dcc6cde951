"use client"

import { useEffect, useState, useRef } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON>, B<PERSON>ble, Sender, Suggestion, Prompts } from '@ant-design/x'
import { chatService, ChatMessage } from "@/lib/services/chatService"
import ReactMarkdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import remarkGfm from 'remark-gfm'

interface Message {
  role: 'user' | 'assistant';
  content: string;
  isLoading?: boolean;
  reasoning_content?: string;
  tool_calls?: Array<{
    index: number;
    id: string;
    type: string;
    function: {
      name: string;
      arguments: string;
    };
  }>;
  tool_results?: Array<{
    tool_call_id: string;
    name: string;
    arguments: Record<string, any>;
    result: any;
  }>;
}

export function AiSuggestionDialog({ questions, client, isConnected, isAuthenticated, setLoginDialogOpen }: { questions: string[], client: any, isConnected: boolean, isAuthenticated: boolean, setLoginDialogOpen: (open: boolean) => void }) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [tools, setTools] = useState<any[]>([]);
  const [userScrolled, setUserScrolled] = useState(false);
  const [expandedToolCalls, setExpandedToolCalls] = useState<Record<string, boolean>>({});
  const [copiedItems, setCopiedItems] = useState<Record<string, boolean>>({});
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isConnected && client) {
      handleListTools();
    }
  }, [isConnected, client]);

  useEffect(() => {
    // 当有新消息时，重置userScrolled状态，允许自动滚动
    if (messages.length > 0) {
      setUserScrolled(false);
    }
  }, [messages.length]);

  useEffect(() => {
    // 当消息更新且用户未手动滚动时，自动滚动到底部
    if (!userScrolled && chatContainerRef.current) {
      // 使用直接滚动而非scrollIntoView，避免影响页面滚动
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages, userScrolled]);

  const handleListTools = async () => {
    if (!client) return;
    try {
      const response = await client.listTools();
      setTools(response.tools || []);
    } catch (err) {
      console.error('获取工具列表失败:', err);
    }
  };

  const handleCallTool = async (toolName: string, args: Record<string, any>) => {
    if (!client) {
      throw new Error('MCP客户端未连接');
    }
    try {
      console.log('调用工具:', { name: toolName, args });
      const response = await client.callTool(toolName, args);
      console.log('工具响应:', response);
      return response;
    } catch (err) {
      console.error('工具调用错误:', err);
      throw err;
    }
  };

  const handleSendMessage = async (questionOrInputContent?: string) => {
    if(!isAuthenticated) {
      setLoginDialogOpen(true)
      return false;
    }
    
    // 判断是通过提示词点击还是输入框发送
    // 如果参数存在且与输入框内容不同，说明是通过提示词点击发送的
    const isFromPrompt = questionOrInputContent && questionOrInputContent !== inputMessage;
    
    // 使用传入的questionOrInputContent（可能来自提示词或输入框）或输入框中的内容
    const messageContent = questionOrInputContent || inputMessage;
    
    if (!messageContent.trim() || isLoading) return;

    const userMessage: Message = {
      role: 'user',
      content: messageContent
    };

    setMessages(prev => [...prev, userMessage]);
    
    // 只有当不是通过提示词点击发送的消息时才清空输入框
    if (!isFromPrompt) {
      setInputMessage('');
    }
    
    setIsLoading(true);

    // 立即创建一个空的助手消息
    setMessages(prev => [...prev, {
      role: 'assistant',
      content: '',
      isLoading: true,
      tool_calls: []
    }]);

    try {
      if (!client) {
        throw new Error('MCP客户端未连接');
      }

      // 将MCP工具转换为API工具格式
      const apiTools = tools.map((tool: any) => ({
        name: tool.name,
        description: tool.description,
        parameters: tool.inputSchema.properties || {}
      }));

      // 验证消息格式
      const validMessages = [...messages, userMessage].map(msg => ({
        role: msg.role,
        content: msg.content || ''
      }));

      try {
        // 使用chatService处理聊天
        await chatService.chat({
          messages: validMessages as ChatMessage[],
          tools: apiTools.length > 0 ? apiTools : undefined,
        }, {
          onUpdate: (content: string, delta: string, toolCalls?: any[], isNewMessage?: boolean) => {
            setMessages(prev => {
              const newMessages = [...prev];
              if (isNewMessage) {
                // 检查最后一条消息是否为空
                const lastMessage = newMessages[newMessages.length - 1];
                if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.content && !lastMessage.tool_calls?.length) {
                  // 如果最后一条消息为空，更新它
                  lastMessage.content = content;
                  lastMessage.isLoading = false; // 有内容时取消 loading 状态
                  if (toolCalls) {
                    lastMessage.tool_calls = toolCalls;
                  }
                } else {
                  // 否则创建新消息
                  newMessages.push({
                    role: 'assistant',
                    content: content,
                    isLoading: false, // 新消息有内容，不需要 loading
                    tool_calls: toolCalls || []
                  });
                }
              } else {
                // 更新最后一条消息
                const lastMessage = newMessages[newMessages.length - 1];
                if (lastMessage && lastMessage.role === 'assistant') {
                  if (typeof content === 'string') {
                    lastMessage.content = content;
                    lastMessage.isLoading = false; // 有内容时取消 loading 状态
                  } else if (typeof content === 'object') {
                    lastMessage.content = JSON.stringify(content);
                    lastMessage.isLoading = false; // 有内容时取消 loading 状态
                  }
                }
              }
              return newMessages;
            });
          },
          onFinish: async (content, toolCalls) => {
            // 先渲染第一次调用的结果
            setMessages(prev => {
              const newMessages = [...prev];
              const lastMessage = newMessages[newMessages.length - 1];
              if (lastMessage && lastMessage.role === 'assistant') {
                lastMessage.content = content;
                lastMessage.isLoading = false; // 完成时取消 loading 状态
                lastMessage.tool_calls = toolCalls || [];
              }
              return newMessages;
            });

            // 如果有工具调用，处理工具调用
            if (toolCalls && toolCalls.length > 0) {
              // 创建一条新的消息用于显示工具调用结果
              setMessages(prev => [...prev, {
                role: 'assistant',
                content: '',
                isLoading: true, // 工具调用结果也需要 loading 状态
                tool_calls: []
              }]);
              
              // 使用chatService处理工具调用
              try {
                const toolCallResults = await chatService.handleToolCalls(
                  handleCallTool,
                  toolCalls,
                  validMessages as ChatMessage[],
                  {
                    onUpdate: (content, delta) => {
                      // 更新最后一条消息的内容
                      setMessages(prev => {
                        const newMessages = [...prev];
                        const lastMessage = newMessages[newMessages.length - 1];
                        if (lastMessage && lastMessage.role === 'assistant') {
                          if (typeof content === 'string') {
                            lastMessage.content = content;
                            lastMessage.isLoading = false; // 有内容时取消 loading 状态
                          } else if (typeof content === 'object') {
                            lastMessage.content = JSON.stringify(content);
                            lastMessage.isLoading = false; // 有内容时取消 loading 状态
                          }
                        }
                        return newMessages;
                      });
                    },
                    onFinish: (formattedContent) => {
                      // 更新最后一条消息的内容
                      setMessages(prev => {
                        const newMessages = [...prev];
                        const lastMessage = newMessages[newMessages.length - 1];
                        if (lastMessage && lastMessage.role === 'assistant') {
                          lastMessage.content = formattedContent;
                          lastMessage.isLoading = false; // 完成时取消 loading 状态
                        }
                        return newMessages;
                      });
                    },
                    onError: (error) => {
                      console.error('格式化错误:', error);
                      setMessages(prev => {
                        const newMessages = [...prev];
                        const lastMessage = newMessages[newMessages.length - 1];
                        if (lastMessage && lastMessage.role === 'assistant') {
                          lastMessage.content = '工具调用结果格式化失败';
                          lastMessage.isLoading = false; // 错误时也取消 loading 状态
                        }
                        return newMessages;
                      });
                    }
                  }
                );
                
                // 工具调用完成后，将结果保存到包含工具调用的消息中
                if (toolCallResults && toolCallResults.length > 0) {
                  const toolResults = toolCallResults.map((result, index) => ({
                    tool_call_id: toolCalls[index].id,
                    name: result.tool,
                    arguments: result.args,
                    result: result.result
                  }));
                  
                  setMessages(prev => {
                    const newMessages = [...prev];
                    // 找到包含工具调用的消息（倒数第二条）
                    const toolCallMessageIndex = newMessages.length - 2;
                    if (toolCallMessageIndex >= 0 && newMessages[toolCallMessageIndex].tool_calls) {
                      newMessages[toolCallMessageIndex].tool_results = toolResults;
                    }
                    return newMessages;
                  });
                }
                
                setIsLoading(false);
              } catch (error) {
                console.error('工具调用处理错误:', error);
                setMessages(prev => {
                  const newMessages = [...prev];
                  const lastMessage = newMessages[newMessages.length - 1];
                  if (lastMessage && lastMessage.role === 'assistant') {
                    lastMessage.content = '工具调用处理失败';
                    lastMessage.isLoading = false; // 错误时取消 loading 状态
                  }
                  return newMessages;
                });
                setIsLoading(false);
              }
            } else {
              setIsLoading(false);
            }
          },
          onError: (error) => {
            console.error('聊天错误:', error);
            setIsLoading(false);
          }
        });
      } catch (error) {
        console.error('聊天请求失败:', error);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('发送消息错误:', error);
      let errorMessage = '抱歉，发生了错误';
      if (error instanceof Error) {
        if (error.message.includes('OpenAI API key is not configured')) {
          errorMessage = 'OpenAI API密钥未配置，请在环境变量中设置 OPENAI_API_KEY';
        } else {
          errorMessage = `错误：${error.message}`;
        }
      }
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: errorMessage,
        isLoading: false // 错误消息不需要 loading 状态
      }]);
      setIsLoading(false);
    }
  };

  const handlePresetQuestion = (question: string) => {
    setInputMessage(question);
    // 如果需要立即发送问题，可以在这里调用handleSendMessage
  };

  // 复制到剪贴板的函数
  const copyToClipboard = async (text: string, itemId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItems(prev => ({ ...prev, [itemId]: true }));
      // 2秒后重置复制状态
      setTimeout(() => {
        setCopiedItems(prev => ({ ...prev, [itemId]: false }));
      }, 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  // 用于渲染Markdown内容的函数
  const renderMarkdown = (content?: string, message?: Message, messageIndex?: number) => {
    if (!content) return null;
    
    // 用户消息直接显示文本
    if (message?.role === 'user') {
      return <div>{content}</div>;
    }
    
    // 工具调用消息的特殊渲染
    if (message?.tool_calls && message.tool_calls.length > 0 && (!message.content || message.content === '')) {
      const messageId = `tool-call-${messageIndex}-${message.tool_calls[0].id}`;
      const isExpanded = expandedToolCalls[messageId] ?? false;
      
      const toggleExpanded = () => {
        setExpandedToolCalls(prev => ({
          ...prev,
          [messageId]: !prev[messageId]
        }));
      };
      
      return (
        <div className="tool-calls-container">
          <div 
            className="tool-calls-header" 
            style={{ 
              fontSize: '13px', 
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}
            onClick={toggleExpanded}
          >
            <span style={{ 
              transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s ease',
              fontSize: '10px'
            }}>
              ▶
            </span>
            🔧 模型调用工具 ({message.tool_calls.length}个)
          </div>
          
          {isExpanded && (
            <div style={{ marginTop: '8px' }}>
              {message.tool_calls.map((toolCall, index) => {
                let parsedArgs = {};
                try {
                  parsedArgs = JSON.parse(toolCall.function.arguments);
                } catch (e) {
                  parsedArgs = { raw: toolCall.function.arguments };
                }
                
                const toolResult = message.tool_results?.find(result => result.tool_call_id === toolCall.id);
                const inputId = `input-${messageIndex}-${toolCall.id}`;
                const outputId = `output-${messageIndex}-${toolCall.id}`;
                const inputText = JSON.stringify(parsedArgs, null, 2);
                const outputText = toolResult ? (typeof toolResult.result === 'string' 
                  ? toolResult.result 
                  : JSON.stringify(toolResult.result, null, 2)) : '';
                
                return (
                  <div key={toolCall.id} className="tool-call-item">
                    <div>
                      {toolCall.function.name}
                    </div>
                    
                    {Object.keys(parsedArgs).length > 0 && (
                      <div style={{ marginBottom: '8px' }}>
                        <div style={{ 
                          fontSize: '12px', 
                          marginBottom: '4px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between'
                        }}>
                          <span>输入:</span>
                          <button
                            onClick={() => copyToClipboard(inputText, inputId)}
                            className={copiedItems[inputId] ? 'copied' : ''}
                          >
                            {copiedItems[inputId] ? '✓ 已复制' : '📋 复制'}
                          </button>
                        </div>
                        <div style={{ position: 'relative' }}>
                          <pre>
                            {inputText}
                          </pre>
                        </div>
                      </div>
                    )}
                    
                    {toolResult && (
                      <div>
                        <div style={{ 
                          fontSize: '12px', 
                          marginBottom: '4px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between'
                        }}>
                          <span>输出:</span>
                          <button
                            onClick={() => copyToClipboard(outputText, outputId)}
                            className={copiedItems[outputId] ? 'copied' : ''}
                          >
                            {copiedItems[outputId] ? '✓ 已复制' : '📋 复制'}
                          </button>
                        </div>
                        <div style={{ position: 'relative' }}>
                          <pre>
                            {outputText}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      );
    }
    
    // 工具调用消息直接显示文本（兼容旧逻辑）
    if (content.startsWith('模型调用工具：')) {
      return <div>{content}</div>;
    }
    
    // AI消息使用 ReactMarkdown 渲染
    // 如果是 AI 消息且内容为空且正在加载，显示 loading 状态
    if (message?.role === 'assistant' && (!content || content.trim() === '') && message?.isLoading) {
      return (
        <div className="markdown-content loading-content" style={{ fontSize: '14px', lineHeight: 1.6 }}>
          <div className="loading-indicator">
            <div className="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span className="loading-text">AI 正在思考中...</span>
          </div>
        </div>
      );
    }
    
    return (
      <div className="markdown-content" style={{ fontSize: '14px', lineHeight: 1.6 }}>
        <ReactMarkdown 
          rehypePlugins={[rehypeRaw]} 
          remarkPlugins={[remarkGfm]}
          components={{
            a: ({node, ...props}) => (
              <a {...props} target="_blank" rel="noopener noreferrer" />
            )
          }}
        >
          {content}
        </ReactMarkdown>
      </div>
    );
  };

  // 将消息转换为Bubble.List需要的items格式
  const bubbleItems = messages.map((message, index) => {
    let displayContent = message.content;
    let isMessageLoading = false;
    
    // 如果存在工具调用且内容为空，将工具调用信息添加到内容中
    if (message.tool_calls && message.tool_calls.length > 0 && (!message.content || message.content === '')) {
      displayContent = `模型调用工具：${message.tool_calls.map(call => call.function.name).join(', ')}`;
      // 即使在loading状态也显示工具调用信息
      isMessageLoading = false;
    } else if (message.role === 'assistant' && (!message.content || message.content === '') && message.isLoading) {
      // 如果是 AI 消息且内容为空且该消息正在加载，显示loading状态
      isMessageLoading = true;
    }
    
    const item: any = {
      placement: message.role === 'user' ? 'end' : 'start',
      content: displayContent,
      variant: message.role === 'user' ? 'filled' : 'outlined',
      messageRender: (content: string) => renderMarkdown(content, message, index), // 传递message对象
    };
    
    if (isMessageLoading) {
      item.loading = true;
    }
    return item;
  });

  // const presetQuestions = [
  //   {
  //     icon: "☕",
  //     iconColor: "text-amber-600",
  //     label: "如何在本地环境中配置此MCP服务?"
  //   },
  //   {
  //     icon: "💡",
  //     iconColor: "text-yellow-500",
  //     label: "此MCP服务有哪些常见应用场景?"
  //   },
  //   {
  //     icon: "🔥",
  //     iconColor: "text-red-500",
  //     label: "如何解决使用过程中的常见问题?"
  //   }
  // ];

  return (
    <XProvider>
      <Card className="card-hover border-t-4 border-t-accent/70">
        <CardHeader>
          <CardTitle className="text-accent flex items-center gap-2">在线试一试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="min-h-0 max-h-[450px] rounded-md mb-4 p-4 overflow-auto" ref={chatContainerRef}>
            <style jsx global>{`
              .markdown-content {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                color: hsl(var(--foreground));
              }
              .markdown-content h1,
              .markdown-content h2,
              .markdown-content h3,
              .markdown-content h4,
              .markdown-content h5,
              .markdown-content h6 {
                margin-top: 16px;
                margin-bottom: 8px;
                font-weight: 600;
                color: hsl(var(--foreground));
              }
              .markdown-content h1 { font-size: 1.5em; }
              .markdown-content h2 { font-size: 1.3em; }
              .markdown-content h3 { font-size: 1.15em; }
              .markdown-content p { 
                margin-bottom: 12px; 
                color: hsl(var(--foreground));
              }
              .markdown-content code {
                background-color: hsl(var(--muted));
                border-radius: 3px;
                padding: 2px 4px;
                font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
                font-size: 90%;
                color: hsl(var(--foreground));
              }
              .markdown-content pre {
                background-color: hsl(var(--muted));
                border-radius: 4px;
                padding: 12px;
                overflow-x: auto;
                margin-bottom: 12px;
                color: hsl(var(--foreground));
              }
              .markdown-content pre code {
                background-color: transparent;
                padding: 0;
                display: block;
                line-height: 1.5;
                color: hsl(var(--foreground));
              }
              .markdown-content a {
                color: hsl(var(--primary));
                text-decoration: none;
              }
              .markdown-content a:hover {
                text-decoration: underline;
                color: hsl(var(--accent));
              }
              .markdown-content ul, .markdown-content ol {
                padding-left: 24px;
                margin-bottom: 12px;
                color: hsl(var(--foreground));
              }
              .markdown-content li {
                margin-bottom: 4px;
                color: hsl(var(--foreground));
              }
              .markdown-content blockquote {
                border-left: 4px solid hsl(var(--border));
                padding-left: 12px;
                color: hsl(var(--muted-foreground));
                margin-left: 0;
                margin-bottom: 12px;
              }
              .markdown-content img {
                max-width: 100%;
                height: auto;
                display: block;
                margin: 16px 0;
              }
              .markdown-content table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 12px;
                margin-top: 12px;
                table-layout: fixed;
                word-break: break-word;
                overflow-wrap: break-word;
                color: hsl(var(--foreground));
              }
              .markdown-content table th,
              .markdown-content table td {
                border: 1px solid hsl(var(--border));
                padding: 8px;
                vertical-align: top;
                line-height: 1.5;
                min-width: 80px;
                color: hsl(var(--foreground));
              }
              .markdown-content table th {
                background-color: hsl(var(--muted));
                text-align: left;
                font-weight: 600;
                color: hsl(var(--foreground));
              }
              .markdown-content table thead {
                border-bottom: 2px solid hsl(var(--border));
              }
              /* 工具调用头部样式 */
              .tool-calls-container .tool-calls-header {
                color: hsl(var(--muted-foreground));
              }
              .tool-calls-container .tool-calls-header:hover {
                background-color: transparent;
                border-radius: 4px;
                padding: 4px 8px;
                margin: -4px -8px -4px -8px;
              }
              /* Loading 状态样式 */
              .loading-content .loading-indicator {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 12px 0;
                color: hsl(var(--muted-foreground));
              }
              .loading-content .loading-dots {
                display: flex;
                gap: 4px;
              }
              .loading-content .loading-dots span {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: hsl(var(--primary));
                animation: loading-bounce 1.4s ease-in-out infinite both;
              }
              .loading-content .loading-dots span:nth-child(1) {
                animation-delay: -0.32s;
              }
              .loading-content .loading-dots span:nth-child(2) {
                animation-delay: -0.16s;
              }
              .loading-content .loading-text {
                font-size: 13px;
                font-weight: 500;
                color: hsl(var(--muted-foreground));
              }
              @keyframes loading-bounce {
                0%, 80%, 100% {
                  transform: scale(0.8);
                  opacity: 0.5;
                }
                40% {
                  transform: scale(1);
                  opacity: 1;
                }
              }
              /* 工具调用容器样式 */
              .tool-calls-container .tool-call-item {
                border: 1px solid hsl(var(--border));
                background-color: hsl(var(--card));
                border-radius: 6px;
                padding: 12px;
                margin-bottom: 8px;
                color: hsl(var(--card-foreground));
              }
              .tool-calls-container .tool-call-item:last-child {
                margin-bottom: 0;
              }
              .tool-calls-container .tool-call-item > div:first-child {
                color: hsl(var(--foreground));
                font-weight: 600;
                margin-bottom: 8px;
              }
              .tool-calls-container .tool-call-item > div > div:first-child {
                color: hsl(var(--muted-foreground));
              }
              .tool-calls-container .tool-call-item > div > div > span {
                color: hsl(var(--muted-foreground));
              }
              .tool-calls-container .tool-call-item > div > div > button {
                color: hsl(var(--primary));
                background: none;
                border: none;
                cursor: pointer;
                font-size: 12px;
                padding: 2px 6px;
                border-radius: 3px;
                transition: all 0.2s ease;
              }
              .tool-calls-container .tool-call-item > div > div > button:hover {
                background-color: hsl(var(--muted));
              }
              .tool-calls-container .tool-call-item > div > div > button.copied {
                color: hsl(var(--accent));
              }
              .tool-calls-container .tool-call-item pre {
                background-color: hsl(var(--muted));
                border: 1px solid hsl(var(--border));
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
                overflow: auto;
                margin: 0;
                color: hsl(var(--foreground));
              }
              .tool-calls-container .tool-call-item pre:last-child {
                background-color: hsl(var(--accent) / 0.1);
                border-color: hsl(var(--accent) / 0.3);
              }
              /* Prompts 组件标题样式 */
              .ant-prompts-title {
                color: hsl(var(--foreground)) !important;
              }
              /* 针对 Ant Design X 的 Prompts 组件标题样式 */
              .ant-x-prompts-title,
              .ant-x-prompts .ant-x-prompts-title {
                color: hsl(var(--foreground)) !important;
              }
              /* 更多可能的 Prompts 标题选择器 */
              .ant-prompts .ant-prompts-title,
              .ant-x-prompts-header,
              .ant-x-prompts .ant-x-prompts-header,
              .ant-prompts-header,
              .ant-prompts .ant-prompts-header,
              [class*="prompts"] [class*="title"],
              [class*="x-prompts"] [class*="title"] {
                color: hsl(var(--foreground)) !important;
              }
              /* 通用的标题样式覆盖 */
              .ant-x-prompts *,
              .ant-prompts * {
                color: inherit;
              }
              .ant-x-prompts,
              .ant-prompts {
                color: hsl(var(--foreground));
              }
              /* 根据实际 DOM 结构的样式修复 */
              .ant-prompts .ant-prompts-title.ant-typography {
                color: hsl(var(--foreground)) !important;
              }
              /* Prompts 选项样式 */
              .ant-prompts-item {
                background-color: hsl(var(--card)) !important;
                border-color: hsl(var(--border)) !important;
                color: hsl(var(--card-foreground)) !important;
              }
              .ant-prompts-item:hover {
                background-color: hsl(var(--accent)) !important;
                color: hsl(var(--accent-foreground)) !important;
              }
              .ant-prompts-label {
                color: hsl(var(--card-foreground)) !important;
              }
              .ant-prompts-item:hover .ant-prompts-label {
                color: hsl(var(--accent-foreground)) !important;
              }
              /* Bubble 组件用户消息样式 */
              .ant-bubble-end .ant-bubble-content {
                background-color: transparent !important;
                border: 1px solid hsl(var(--primary)) !important;
                color: hsl(var(--primary)) !important;
              }
              .ant-bubble-end .ant-bubble-content-filled {
                background-color: transparent !important;
                border: 1px solid hsl(var(--primary)) !important;
                color: hsl(var(--primary)) !important;
              }
              .ant-bubble-end .ant-bubble-content div,
              .ant-bubble-end .ant-bubble-content-filled div {
                color: hsl(var(--primary)) !important;
              }
              /* AI 回复消息样式 */
              .ant-bubble-start .ant-bubble-content {
                background-color: hsl(var(--card)) !important;
                border-color: hsl(var(--border)) !important;
                color: hsl(var(--card-foreground)) !important;
              }
              .ant-bubble-start .ant-bubble-content div {
                color: hsl(var(--card-foreground)) !important;
              }
            `}</style>
            <Bubble.List 
              items={bubbleItems}
              onScroll={(e) => {
                const target = e.currentTarget;
                // 检测用户是否手动滚动
                const isScrolledToBottom = target.scrollHeight - target.scrollTop <= target.clientHeight + 50;
                setUserScrolled(!isScrolledToBottom);
              }}
              roles={{
                user: {
                  variant: 'filled',
                  placement: 'end',
                },
                assistant: {
                  variant: 'outlined',
                  placement: 'start',
                  messageRender: renderMarkdown,
                }
              }}
            />
            <div ref={messagesEndRef} />
          </div>
          <Prompts
            title="🤔 你可以试试这么问："
            wrap
            items={questions.map((item, index) => {
              return {
                key: index.toString(),
                label: item
              }
            })}
            onItemClick={(info) => {
              // 根据Ant Design X文档，使用info.data获取提示词信息
              if (info.data && (typeof info.data.label === 'string' || typeof info.data.description === 'string')) {
                const questionText = info.data.label || info.data.description;
                // 直接发送提示词内容，通过isFromPrompt逻辑判断不会清空输入框
                handleSendMessage(questionText as string);
              }
            }}
          />
          <div className="relative mt-4">
            <Sender
              loading={isLoading}
              value={inputMessage}
              onChange={setInputMessage}
              onSubmit={handleSendMessage}
              onCancel={() => setIsLoading(false)}
              autoSize={{ minRows: 2, maxRows: 6 }} 
              placeholder="输入您的问题..." 
              disabled={(isAuthenticated && !isConnected) || isLoading}
              styles={{
                input: {
                  color: 'inherit'
                }
              }}
              classNames={{
                input: 'text-gray-900 dark:text-white dark:placeholder:text-gray-400'
              }}
            />
            <div className="text-xs text-muted-foreground mt-2">
              {questions.length? '您可选择一个预设对话，或者用您自己的语言要求AI使用本MCP提供的工具' : ''}
              {(isAuthenticated &&!isConnected) && <span className="text-red-500 ml-1">（未连接到MCP服务器）</span>}
            </div>
          </div>
        </CardContent>
      </Card>
    </XProvider>
  )
}