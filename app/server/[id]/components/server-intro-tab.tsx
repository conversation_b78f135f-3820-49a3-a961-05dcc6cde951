import { Card, CardContent } from "@/components/ui/card"
import { Github, Earth } from "@icon-park/react"
import ReactMarkdown from "react-markdown"
import rehypeRaw from "rehype-raw"

type ServerIntroTabProps = {
  detail: string
  github: string | null
  website: string | null
}

export function ServerIntroTab({ detail, github, website }: ServerIntroTabProps) {
  return (
    <Card className="mb-6 relative border-t-0 rounded-tl-none">
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div>
            <div className="prose prose-sm dark:prose-invert max-w-none prose-headings:font-semibold prose-a:text-primary hover:prose-a:text-primary/80 prose-strong:text-primary prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-primary prose-img:rounded-lg prose-pre:bg-muted prose-pre:border prose-pre:border-border">
              <ReactMarkdown rehypePlugins={[rehypeRaw]}>{detail}</ReactMarkdown>
            </div>
          </div>

          {/* 只有当github或website存在时才显示分隔线和链接 */}
          {(github || website) && (
            <div className="pt-4 mt-6 border-t">
              <div className="flex items-center gap-4 text-sm">
                {github && (
                  <a
                    href={github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-muted-foreground hover:text-primary transition-colors"
                  >
                    <Github theme="outline" size="16" />
                    <span>GitHub</span>
                  </a>
                )}
                {website && (
                  <a
                    href={website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-muted-foreground hover:text-primary transition-colors"
                  >
                    <Earth theme="outline" size="16" />
                    <span>官网</span>
                  </a>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
