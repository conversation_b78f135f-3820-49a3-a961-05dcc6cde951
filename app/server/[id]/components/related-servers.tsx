import { serverData } from "@/lib/data"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "@icon-park/react"
import projectsApi from "@/lib/api/apis/projects"
import { useEffect, useState } from "react"
type RelatedServersProps = {
  servers: any[]
}

export function RelatedServers({ servers }: RelatedServersProps) {
  // const [servers, setServers] = useState<any[]>([])
  // const getRelatedServers = async () => {
  //   const params = {
  //     page: 1,
  //     pageSize: 4,
  //     category,
  //     orderBy: "random",
  //   }
  //   const response = await projectsApi.getProjectsList(params)
  //   if (response && response.data) {
  //     let arr = response.data.list.filter((server: any) => server.ID !== currentServerId);
  //     setServers(arr.splice(0, 3))
  //   }
  // }
  // useEffect(() => {
  //   getRelatedServers()
  // }, [])

  return (
    <Card className="card-hover border-t-4 border-t-secondary/70 mt-6">
      <CardHeader>
        <CardTitle className="text-secondary flex items-center gap-2">相关服务推荐</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {servers.map((server) => (
          <Link href={`/server/${server.uuid}`} key={server.uuid} className="block">
            <div className="border rounded-md p-3 hover:border-secondary hover:bg-secondary/5 transition-colors">
              <div className="flex items-start gap-2">
                <div className="w-8 h-8 rounded-md overflow-hidden flex-shrink-0">
                  <img
                    src={server.logoUrl || "/placeholder.svg"}
                    alt={`${server.name} logo`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-sm text-primary">{server.name}</h3>
                  <p className="text-xs text-muted-foreground line-clamp-1">{server.description}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <Calendar theme="outline" size="12" className="text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      {server.UpdatedAt}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </CardContent>
    </Card>
  )
}
