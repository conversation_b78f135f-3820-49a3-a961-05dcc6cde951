"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Calendar, Earth, Computer, Like } from "@icon-park/react"
import Link from "next/link"
import { useState } from "react"

type ServerHeaderProps = {
  server: {
    id: number
    name: string
    description: string
    logo: string
    updatedAt: string
    tags: string[]
    callMethod: string[]
    onlineUsageCount: number
    offlineUsageCount: number
    likes: number
  }
}

export function ServerHeader({ server }: ServerHeaderProps) {
  const [liked, setLiked] = useState(false)
  const [likeCount, setLikeCount] = useState(server.likes)
  const [likeAnimating, setLikeAnimating] = useState(false)

  // 处理点赞
  const handleLike = () => {
    // 获取已点赞的服务器列表
    const likedServers = localStorage.getItem("likedServers")
    let likedArray = likedServers ? JSON.parse(likedServers) : []

    if (!liked) {
      // 点赞
      setLikeCount((prev) => prev + 1)
      setLiked(true)
      setLikeAnimating(true)
      setTimeout(() => setLikeAnimating(false), 300)

      // 添加到已点赞列表
      likedArray.push(server.id)
    } else {
      // 取消点赞
      setLikeCount((prev) => prev - 1)
      setLiked(false)

      // 从已点赞列表中移除
      likedArray = likedArray.filter((id: number) => id !== server.id)
    }

    // 保存到本地存储
    localStorage.setItem("likedServers", JSON.stringify(likedArray))
  }

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, "0")
    const day = String(date.getDate()).padStart(2, "0")
    const hours = String(date.getHours()).padStart(2, "0")
    const minutes = String(date.getMinutes()).padStart(2, "0")
    const seconds = String(date.getSeconds()).padStart(2, "0")

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  return (
    <Card className="mb-4 relative border-t-4 border-t-primary/70">
      <div className="absolute top-6 right-6 flex items-center gap-4">
        {server.callMethod.includes("local") && (
          <div className="tooltip-trigger relative">
            <div className="flex items-center gap-1 text-primary">
              <Computer theme="outline" size="16" />
              <span className="text-sm font-medium">{server.offlineUsageCount.toLocaleString()}</span>
            </div>
            <div className="custom-tooltip absolute top-full right-0 mt-2 px-2 py-1 bg-black text-white text-xs rounded pointer-events-none whitespace-nowrap z-50">
              本地安装次数
            </div>
          </div>
        )}
        {server.callMethod.includes("online") && (
          <div className="tooltip-trigger relative">
            <div className="flex items-center gap-1 text-primary">
              <Earth theme="outline" size="16" />
              <span className="text-sm font-medium">{server.onlineUsageCount.toLocaleString()}</span>
            </div>
            <div className="custom-tooltip absolute top-full right-0 mt-2 px-2 py-1 bg-black text-white text-xs rounded pointer-events-none whitespace-nowrap z-50">
              在线调用次数
            </div>
          </div>
        )}
      </div>

      <CardHeader>
        <div>
          <CardTitle className="text-2xl flex items-center gap-3 text-primary">
            <div className="w-8 h-8 rounded-md overflow-hidden flex-shrink-0">
              <img
                src={server.logo || "/placeholder.svg"}
                alt={`${server.name} logo`}
                className="w-full h-full object-cover"
              />
            </div>
            {server.name}
          </CardTitle>
          <CardDescription className="text-base mt-2">{server.description}</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar theme="outline" size="16" />
            最后更新: {formatDateTime(server.updatedAt)}
          </div>
        </div>

        <Separator />

        <div className="flex items-center justify-between">
          <div className="flex flex-wrap gap-2">
            {server.tags.map((tag) => (
              <Link href={`/?tag=${encodeURIComponent(tag)}`} key={tag} className="no-underline">
                <Badge
                  variant="outline"
                  className="cursor-pointer hover:bg-primary/10 hover:text-primary transition-colors"
                >
                  {tag}
                </Badge>
              </Link>
            ))}
          </div>

          {/* 点赞按钮 */}
          <button
            onClick={handleLike}
            className="flex items-center gap-1.5 text-muted-foreground hover:text-red-500 transition-colors"
          >
            <Like
              theme={liked ? "filled" : "outline"}
              size="20"
              className={`
                ${liked ? "text-red-500" : ""} 
                ${likeAnimating ? "scale-125" : "scale-100"} 
                transition-all duration-300
              `}
            />
            <span className="font-medium">{likeCount}</span>
          </button>
        </div>
      </CardContent>
    </Card>
  )
}
