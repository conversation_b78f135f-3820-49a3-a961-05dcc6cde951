"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Left, Copy, AppleOne, Windows, Earth, Computer } from "@icon-park/react"

type ServerInstallConfigProps = {
  server: {
    package: string
    callMethod: string[]
  }
  showInstallConfig: boolean
  showApiUrl: boolean
  setShowInstallConfig: (show: boolean) => void
  setShowApiUrl: (show: boolean) => void
  userApiKey?: string
  isAuthenticated: boolean
  setLoginDialogOpen: (open: boolean) => void
}

export function ServerInstallConfig({
  server,
  showInstallConfig,
  showApiUrl,
  setShowInstallConfig,
  setShowApiUrl,
  userApiKey,
  isAuthenticated,
  setLoginDialogOpen,
}: ServerInstallConfigProps) {
  const [selectedOS, setSelectedOS] = useState<"mac" | "windows">("mac")
  const [copied, setCopied] = useState(false)

  // JSON配置
  const macConfig = `{
 "mcpServers": {
   "server-sequential-thinking": {
     "command": "npx",
     "args": [
       "-y",
       "@smithery/cli@latest",
       "run",
       "@smithery-ai/server-sequential-thinking",
       "--key",
       "21b859ed-62d2-4101-98c3-ea1815d58e5e"
     ]
   }
 }
}`

  const windowsConfig = `{
 "mcpServers": {
   "server-sequential-thinking": {
     "command": "cmd",
     "args": [
       "/c",
       "npx",
       "-y",
       "@smithery/cli@latest",
       "run",
       "@smithery-ai/server-sequential-thinking",
       "--key",
       "21b859ed-62d2-4101-98c3-ea1815d58e5e"
     ]
   }
 }
}`

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      })
      .catch((err) => {
        console.error("复制失败:", err)
      })
  }

  return (
    <Card className="card-hover border-t-4 border-t-secondary/70">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        {showInstallConfig || showApiUrl ? (
          <div className="flex items-center">
            <button
              onClick={() => {
                setShowInstallConfig(false)
                setShowApiUrl(false)
              }}
              className="text-muted-foreground hover:text-primary flex items-center mr-2"
            >
              <Left theme="outline" size="16" className="mr-1" />
              <span>返回</span>
            </button>
          </div>
        ) : (
          <CardTitle className="text-secondary">客户端调用方式</CardTitle>
        )}
      </CardHeader>

      {!showInstallConfig ? (
        <>
          <CardContent className="space-y-4 p-6">
            {showApiUrl ? (
              <div className="relative">
                <div className="flex justify-between items-center mb-2">
                  <div className="text-primary text-sm font-medium">API调用地址</div>
                  <button
                    className={`text-muted-foreground hover:text-primary transition-all duration-200 flex items-center ${copied ? "text-green-500" : ""}`}
                    onClick={() =>
                      copyToClipboard(
                        "https://call.mcpcn.ai/" + server.package + "?key=" + userApiKey || "your-api-key",
                      )
                    }
                  >
                    <Copy theme="outline" size="16" className="mr-1" />
                    <span className="text-xs">{copied ? "已复制" : "复制"}</span>
                  </button>
                </div>
                <div className="relative rounded-md overflow-hidden">
                  <pre className="bg-muted/30 p-4 rounded-md text-sm font-mono break-all whitespace-pre-wrap overflow-x-auto max-w-full">
                    https://call.mcpcn.ai/{server.package}?key={userApiKey || "your-api-key"}
                  </pre>
                </div>
              </div>
            ) : (
              <>
                {server.callMethod.includes("local") && (
                  <Button
                    variant="outline"
                    className="w-full border-2 border-secondary/50 hover:border-secondary hover:bg-secondary/10 hover:text-secondary focus:ring-2 focus:ring-secondary/50 active:bg-secondary/20 h-12 text-base font-medium transition-all duration-200"
                    onClick={() => setShowInstallConfig(true)}
                  >
                    <Computer theme="outline" size="18" className="mr-2" />
                    本地安装
                  </Button>
                )}
                {server.callMethod.includes("online") && (
                  <Button
                    variant="outline"
                    className="w-full border-2 border-secondary/50 hover:border-secondary hover:bg-secondary/10 hover:text-secondary focus:ring-2 focus:ring-secondary/50 active:bg-secondary/20 h-12 text-base font-medium transition-all duration-200"
                    onClick={() => {
                      if (isAuthenticated) {
                        setShowApiUrl(true)
                      } else {
                        setLoginDialogOpen(true)
                      }
                    }}
                  >
                    <Earth theme="outline" size="18" className="mr-2" />
                    在线调用
                  </Button>
                )}
                {server.callMethod.length === 0 && (
                  <div className="text-center py-4 text-muted-foreground">暂无可用的调用方式</div>
                )}
              </>
            )}
          </CardContent>
          <CardFooter></CardFooter>
        </>
      ) : (
        <>
          <CardContent className="p-4">
            <Tabs
              defaultValue={selectedOS}
              onValueChange={(value) => setSelectedOS(value as "mac" | "windows")}
              className="w-full"
            >
              <TabsList className="w-full mb-4 bg-muted border border-border p-1">
                <TabsTrigger
                  value="mac"
                  className="flex-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                >
                  <div className="flex items-center gap-2">
                    <AppleOne theme="outline" size="18" />
                    <span>Mac/Linux</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="windows"
                  className="flex-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                >
                  <div className="flex items-center gap-2">
                    <Windows theme="outline" size="18" />
                    <span>Windows</span>
                  </div>
                </TabsTrigger>
              </TabsList>

              <div className="relative">
                <div className="flex justify-between items-center mb-2">
                  <div className="text-primary text-sm font-medium">
                    {selectedOS === "mac" ? "Mac/Linux 配置" : "Windows 配置"}
                  </div>
                  <button
                    className={`text-muted-foreground hover:text-primary transition-all duration-200 flex items-center ${copied ? "text-green-500" : ""}`}
                    onClick={() => copyToClipboard(selectedOS === "mac" ? macConfig : windowsConfig)}
                  >
                    <Copy theme="outline" size="16" className="mr-1" />
                    <span className="text-xs">{copied ? "已复制" : "复制"}</span>
                  </button>
                </div>
                <div className="relative rounded-md overflow-hidden">
                  <pre className="bg-muted/30 p-4 rounded-md text-sm font-mono break-all whitespace-pre-wrap overflow-x-auto max-w-full">
                    {selectedOS === "mac" ? macConfig : windowsConfig}
                  </pre>
                </div>
              </div>
            </Tabs>
          </CardContent>
        </>
      )}
    </Card>
  )
}
