"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import authApi from "@/lib/api/apis/auth"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Upload, Eyes } from "@icon-park/react"
import fileApi from "@/lib/api/apis/file"
import userApi from "@/lib/api/apis/user"
interface ApiResponse {
  code: number;
  msg?: string;
  data?: any;
}

export default function SettingsPage() {
  const { user, setUser } = useAuth()
  const router = useRouter()
  const [isLoadingNickname, setIsLoadingNickname] = useState(false)
  const [isLoadingPassword, setIsLoadingPassword] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [showOldPassword, setShowOldPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  
  const [nicknameForm, setNicknameForm] = useState({
    nickName: user?.nickName || "",
    headerImg: user?.headerImg || ""
  })

  const [passwordForm, setPasswordForm] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  })

  const [nicknameError, setNicknameError] = useState("")
  const [passwordErrors, setPasswordErrors] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  })
  useEffect(() => {
    setNicknameForm({
      nickName: user?.nickName || "",
      headerImg: user?.headerImg || ""
    })
  }, [user])
  const handleNicknameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target
    setNicknameForm(prev => ({ ...prev, nickName: value }))
    if (nicknameError) {
      setNicknameError("")
    }
  }

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordForm(prev => ({
      ...prev,
      [name]: value
    }))

    // 清除错误信息
    if (passwordErrors[name as keyof typeof passwordErrors]) {
      setPasswordErrors(prev => ({
        ...prev,
        [name]: ""
      }))
    }
  }

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      toast.error('请上传图片文件')
      return
    }

    // 验证文件大小（限制为2MB）
    if (file.size > 5 * 1024 * 1024) {
      toast.error('图片大小不能超过5MB')
      return
    }

    setIsUploading(true)
    try {
      const response: any = await fileApi.upload({file})
      if (response.code === 0) {
        setNicknameForm(prev => ({ ...prev, headerImg: response.data.file.url }))
      } else {
        toast.error(response.msg || '上传失败')
      }
    } catch (error) {
      toast.error('上传失败，请重试')
    } finally {
      setIsUploading(false)
    }
  }

  const validateNickname = () => {
    if (!nicknameForm.nickName) {
      setNicknameError("请输入昵称")
      return false
    }
    return true
  }

  const validatePassword = () => {
    const newErrors = {
      oldPassword: "",
      newPassword: "",
      confirmPassword: ""
    }
    
    let isValid = true
    
    if (user?.havePassword && !passwordForm.oldPassword) {
      newErrors.oldPassword = "请输入原密码"
      isValid = false
    }
    
    if (!passwordForm.newPassword) {
      newErrors.newPassword = "请输入新密码"
      isValid = false
    } else {
      const hasLetter = /[a-zA-Z]/.test(passwordForm.newPassword)
      const hasNumber = /\d/.test(passwordForm.newPassword)
      const hasSpecial = /[^a-zA-Z0-9]/.test(passwordForm.newPassword)
      const count = [hasLetter, hasNumber, hasSpecial].filter(Boolean).length
      if (count < 2 || passwordForm.newPassword.length < 6) {
        newErrors.newPassword = '密码必须包含数字和字母或特殊字符，长度至少6位'
        isValid = false
      } else if(user?.havePassword && passwordForm.oldPassword && passwordForm.newPassword === passwordForm.oldPassword) {
        newErrors.newPassword = "新密码不能与原密码相同"
        isValid = false
      }
    }
    
    if (!passwordForm.confirmPassword) {
      newErrors.confirmPassword = "请确认新密码"
      isValid = false
    } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      newErrors.confirmPassword = "两次输入的密码不一致"
      isValid = false
    }
    
    setPasswordErrors(newErrors)
    return isValid
  }

  const handleNicknameSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateNickname()) {
      return
    }
    
    setIsLoadingNickname(true)
    
    try {
      const response: any = await authApi.setSelfInfo({ 
        nickName: nicknameForm.nickName,
        headerImg: nicknameForm.headerImg
      })
      if (response.code === 0) {
        // 更新全局状态
        const updatedUser = {
          ...user,
          nickName: nicknameForm.nickName,
          headerImg: nicknameForm.headerImg
        }
        setUser(updatedUser as any)
        
        // 更新 localStorage
        const storedUser = localStorage.getItem('mcp-user')
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser)
        }
        
        toast.success("个人信息更新成功")
        router.refresh()
      } else {
        toast.error(response.msg || "更新失败，请重试")
      }
    } catch (error) {
      toast.error("更新失败，请重试")
    } finally {
      setIsLoadingNickname(false)
    }
  }

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validatePassword()) {
      return
    }
    
    setIsLoadingPassword(true)
    
    try {
      const response: any = await userApi.changePassword({
        ...(user?.havePassword ? { password: passwordForm.oldPassword } : {}),
        newPassword: passwordForm.newPassword
      })
      if (response.code === 0) {
        toast.success("密码修改成功")
        setPasswordForm({
          oldPassword: "",
          newPassword: "",
          confirmPassword: ""
        })
        router.refresh()
      } else {
        toast.error(response.msg || "更新失败，请重试")
      }
    } catch (error) {
      toast.error("更新失败，请重试")
    } finally {
      setIsLoadingPassword(false)
    }
  }

  return (
    <>
      <SiteHeader />
      <main className="container mx-auto py-12 px-4 gradient-bg min-h-screen">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="text-2xl">个人设置</CardTitle>
            <CardDescription>修改您的个人信息和密码</CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            {/* 修改个人信息表单 */}
            <form onSubmit={handleNicknameSubmit} className="space-y-4">
              <div className="flex items-center gap-6">
                <div className="relative">
                  <Avatar className="h-20 w-20">
                    <AvatarImage src={nicknameForm.headerImg} alt={nicknameForm.nickName} />
                    <AvatarFallback>{nicknameForm.nickName}</AvatarFallback>
                  </Avatar>
                  <label 
                    htmlFor="avatar-upload" 
                    className="absolute bottom-0 right-0 bg-primary text-primary-foreground p-1 rounded-full cursor-pointer hover:bg-primary/90"
                  >
                    <Upload theme="outline" size="16" />
                  </label>
                  <input
                    id="avatar-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleAvatarUpload}
                    disabled={isUploading}
                  />
                </div>
                <div className="flex-1 space-y-2">
                  <Label htmlFor="nickName">昵称</Label>
                  <Input
                    id="nickName"
                    name="nickName"
                    value={nicknameForm.nickName}
                    onChange={handleNicknameChange}
                    placeholder="请输入昵称"
                    className={nicknameError ? "border-red-500" : ""}
                  />
                  {nicknameError && <p className="text-xs text-red-500">{nicknameError}</p>}
                </div>
              </div>
              <Button type="submit" className="w-full" disabled={isLoadingNickname || isUploading}>
                {isLoadingNickname ? "保存中..." : "保存修改"}
              </Button>
            </form>

            <Separator />

            {/* 修改密码表单 */}
            <form onSubmit={handlePasswordSubmit} className="space-y-4">
              <h3 className="text-lg font-medium">修改密码</h3>
              {user?.havePassword && (
                <div className="space-y-2">
                  <Label htmlFor="oldPassword">原密码</Label>
                  <div className="relative">
                    <Input
                      id="oldPassword"
                      name="oldPassword"
                      type={showOldPassword ? "text" : "password"}
                      value={passwordForm.oldPassword}
                      onChange={handlePasswordChange}
                      placeholder="请输入原密码"
                      className={passwordErrors.oldPassword ? "border-red-500" : ""}
                    />
                    <button
                      type="button"
                      onClick={() => setShowOldPassword(!showOldPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      <Eyes theme="outline" size="16" />
                    </button>
                  </div>
                  {passwordErrors.oldPassword && <p className="text-xs text-red-500">{passwordErrors.oldPassword}</p>}
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="newPassword">{user?.havePassword ? "新密码" : "密码"}</Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type={showNewPassword ? "text" : "password"}
                    value={passwordForm.newPassword}
                    onChange={handlePasswordChange}
                    placeholder={user?.havePassword ? "请输入新密码" : "请输入密码"}
                    className={passwordErrors.newPassword ? "border-red-500" : ""}
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    <Eyes theme="outline" size="16" />
                  </button>
                </div>
                {passwordErrors.newPassword && <p className="text-xs text-red-500">{passwordErrors.newPassword}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">{user?.havePassword ? "确认新密码" : "确认密码"}</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={passwordForm.confirmPassword}
                    onChange={handlePasswordChange}
                    placeholder={user?.havePassword ? "请再次输入新密码" : "请再次输入密码"}
                    className={passwordErrors.confirmPassword ? "border-red-500" : ""}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    <Eyes theme="outline" size="16" />
                  </button>
                </div>
                {passwordErrors.confirmPassword && <p className="text-xs text-red-500">{passwordErrors.confirmPassword}</p>}
              </div>

              <Button type="submit" className="w-full" disabled={isLoadingPassword}>
                {isLoadingPassword ? "保存中..." : user?.havePassword ? "修改密码" : "设置密码"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </main>
      <SiteFooter />
    </>
  )
} 