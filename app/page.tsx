import type React from "react"
import { Suspense } from "react"
import { ClientPageWrapper } from "@/components/client-page-wrapper"
import { HomeSkeleton } from "@/components/home-skeleton"
import projectsApi from "@/lib/api/apis/projects"
import sysDictionaryDetailApi from "@/lib/api/apis/sysDictionaryDetail"

// 强制动态渲染 - 确保每次请求时都重新获取数据
export const dynamic = 'force-dynamic'
export const revalidate = 0

// 服务端数据获取函数
async function getInitialData() {
  try {
    // 获取项目数据和分类数据
    const [projectsResponse, categoriesResponse] = await Promise.all([
      projectsApi.getProjectsList({
        page: 1,
        pageSize: 12,
        orderBy: 'latest'
      }),
      sysDictionaryDetailApi.getDictionaryListWithProjectsCount()
    ])

    const projectsData = (projectsResponse && typeof projectsResponse === 'object' && 'data' in projectsResponse) ? projectsResponse : { data: { list: [], total: 0 } }
    const categoriesData = (categoriesResponse && typeof categoriesResponse === 'object' && 'data' in categoriesResponse) ? categoriesResponse : { data: { list: [], total: 0 } }

    // 使用API返回的分类数据
    const categories = (categoriesData.data as any).list
    const totalProjects = (categoriesData.data as any)?.total || (projectsData.data as any)?.total || 0
    
    return {
      initialProjects: (projectsData.data as any)?.list || [],
      totalCount: (projectsData.data as any)?.total || 0,
      categories: categories,
      totalProjects: totalProjects,
    }
  } catch (error) {
    console.error('服务端数据获取失败:', error)
    
    return {
      initialProjects: [],
      totalCount: 0,
      categories: [],
      totalProjects: 0,
    }
  }
}

// 生成页面元数据
export async function generateMetadata() {
  return {
    title: `精选适合中国用户的 MCP MCP中国 MCP中国社区`,
    description: `致力于构建中国MCP开源社区生态，让您的AI助手轻松使用国内服务`,
    keywords: 'MCP, MCP中国, MCP服务器, MCP客户端, MCP托管, MCP应用案例, 在线调用MCP服务器 ',
    openGraph: {
      title: '精选适合中国用户的 MCP MCP中国 MCP中国社区',
      description: `致力于构建中国MCP开源社区生态，让您的AI助手轻松使用国内服务`,
      type: 'website',
      locale: 'zh_CN',
    }
  }
}

// 主页面组件 - 服务端组件
export default async function MCPNavigationSite() {
  // 在服务端获取初始数据 - 每次请求时都会执行
  const initialData = await getInitialData()

  return (
    <Suspense fallback={<HomeSkeleton />}>
      <ClientPageWrapper 
        initialProjects={initialData.initialProjects}
        initialTotalCount={initialData.totalCount}
        initialCategories={initialData.categories}
        totalProjects={initialData.totalProjects}
      />
    </Suspense>
  )
}
