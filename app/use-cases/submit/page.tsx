"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { Left, Upload, Down, Close, Check } from "@icon-park/react"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import fileApi from "@/lib/api/apis/file"
import projectsApi from "@/lib/api/apis/projects"
import sysDictionaryDetailApi from "@/lib/api/apis/sysDictionaryDetail"
import useCaseApi from "@/lib/api/apis/useCase"
export default function SubmitUseCasePage() {
  const router = useRouter()
  const { isAuthenticated, user, isLoading } = useAuth()

  const [formData, setFormData] = useState({
    // author: user?.name || "",
    homepage: "",
    content: "",
    summary: "",
    videoPlatform: "youtube", // 默认为YouTube
    videoId: "", // 视频ID
    cover: "",
    serverIds: [] as string[],
    clientId: null, // 单选
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [serverPopoverOpen, setServerPopoverOpen] = useState(false)
  const [clientPopoverOpen, setClientPopoverOpen] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [serverData, setServerData] = useState<any[]>([])
  const [clientData, setClientData] = useState<any[]>([])
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }

    // 如果是视频ID发生变化，更新预览URL
    if (name === "videoId" && value.trim()) {
      updatePreviewUrl(value)
    } else if (name === "videoId" && !value.trim()) {
      setPreviewUrl(null) // 如果清空了视频ID，清除预览
    }
  }
  const getServerData = async () => {
    const params = {
      page: 1,
      pageSize: 999,
    }
    
    const response: any = await projectsApi.getProjectsList(params)
    if(response && response.code === 0) {
      setServerData(response.data.list)
    }
  }
  const getClientData = async () => {
    const response: any = await sysDictionaryDetailApi.getMcpClientPublic({
      page: 1,
      pageSize: 999
    })
    if(response && response.code === 0) {
      setClientData(response.data.list)
    }
  }
  useEffect(() => {
    getServerData()
    getClientData()
  }, [])
  // 更新预览URL
  const updatePreviewUrl = (videoId: string) => {
    if (!videoId.trim()) {
      setPreviewUrl(null)
      return
    }

    if (formData.videoPlatform === "youtube") {
      setPreviewUrl(`https://www.youtube.com/embed/${videoId}`)
    } else if (formData.videoPlatform === "BiliBili") {
      setPreviewUrl(`https://player.bilibili.com/player.html?isOutside=true&bvid=${videoId}`)
    }
  }

  // 处理视频平台选择
  const handleVideoPlatformChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      videoPlatform: value,
      videoId: "", // 切换平台时清空视频ID
    }))
    setPreviewUrl(null) // 切换平台时清除预览
  }

  // 处理服务器选择 (多选)
  const handleServerSelect = (serverId: string) => {
    setFormData((prev) => {
      const isSelected = prev.serverIds.includes(serverId)

      if (isSelected) {
        return { ...prev, serverIds: prev.serverIds.filter((id) => id !== serverId) }
      } else {
        return { ...prev, serverIds: [...prev.serverIds, serverId] }
      }
    })
  }

  // 处理客户端选择 (单选)
  const handleClientSelect = (clientId: number) => {
    setFormData((prev: any) => ({
      ...prev,
      clientId: clientId, // 直接替换为新选择的ID
    }))
    setClientPopoverOpen(false) // 选择后关闭下拉框
  }

  // 移除已选择的服务器
  const removeServer = (serverId: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setFormData((prev) => ({
      ...prev,
      serverIds: prev.serverIds.filter((id) => id !== serverId),
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // if (!formData.author.trim()) newErrors.author = "请输入作者名称"
    if (!formData.content.trim()) newErrors.content = "请输入内容"
    if (!formData.summary.trim()) newErrors.summary = "请输入摘要"

    // URL validation
    const urlRegex = /^https:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i

    if (formData.homepage && !urlRegex.test(formData.homepage)) {
      newErrors.homepage = "请输入有效的URL"
    }

    if (formData.videoId && !formData.videoId.trim()) {
      newErrors.videoId = "请输入视频ID"
    }

    if (formData.cover && !urlRegex.test(formData.cover)) {
      newErrors.cover = "请输入有效的封面图URL"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 获取完整的视频URL
  const getVideoUrl = () => {
    if (!formData.videoId) return ""

    if (formData.videoPlatform === "youtube") {
      return `https://www.youtube.com/embed/${formData.videoId}`
    } else if (formData.videoPlatform === "BiliBili") {
      return `https://player.bilibili.com/player.html?isOutside=true&bvid=${formData.videoId}`
    }

    return ""
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsSubmitting(true)

    try {
      // 构建提交数据，包括完整的视频URL
      const submitData = {
        ...formData,
        video: getVideoUrl(),
      }

      console.log(submitData, "submitData")
      const response: any = await useCaseApi.createUseCase(submitData)
      if(response && response.code === 0) {
        // 提交成功后跳转回案例列表页
        router.push("/use-cases")
      } else {
        throw new Error(response?.msg || '提交失败')
      }

    } catch (error) {
      console.error("提交失败:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // 处理文件上传
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({ ...prev, cover: '请上传图片文件' }))
      return
    }

    // 检查文件大小（限制为5MB）
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, cover: '图片大小不能超过5MB' }))
      return
    }

    setIsUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', file, file.name) // 添加文件名
      let response =  await fileApi.upload({file})
      if(response && response.code === 0) {
        setFormData(prev => ({ ...prev, cover: response.data.file.url }))
      } else {
        throw new Error('上传失败')
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, cover: '上传失败，请重试' }))
    } finally {
      setIsUploading(false)
    }
  }

  // 如果未登录，重定向到首页
  useEffect(() => {
    // Only redirect if we've finished loading and the user is not authenticated
    if (!isLoading && !isAuthenticated) {
      router.push("/use-cases")
    }
  }, [isLoading, isAuthenticated, router])

  if (isLoading) {
    return (
      <>
        <SiteHeader />
        <div className="container mx-auto py-12 px-4 gradient-bg min-h-screen">
          <div className="max-w-md mx-auto">
            <div className="w-full h-12 bg-muted/30 animate-pulse rounded-md mb-4"></div>
            <div className="w-full h-64 bg-muted/30 animate-pulse rounded-md"></div>
          </div>
        </div>
        <SiteFooter />
      </>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  // 获取已选服务器的名称
  const selectedServers = serverData.filter((server) => formData.serverIds.includes(server.ID))

  // 获取已选客户端的名称
  const selectedClient = clientData.find((client) => client.ID === formData.clientId)

  return (
    <>
      <SiteHeader />
      <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
        <div className="max-w-3xl mx-auto">
          <div className="mb-6">
            <Link href="/use-cases" className="inline-flex items-center text-muted-foreground hover:text-primary">
              <Left theme="outline" size="16" className="mr-1" />
              <span>返回应用案例</span>
            </Link>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">投稿应用案例</CardTitle>
              <CardDescription>分享您使用MCP服务的经验和成果，帮助更多人了解MCP的应用场景</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
                  {/* <div className="space-y-2">
                    <Label htmlFor="author">
                      作者名称 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="author"
                      name="author"
                      value={formData.author}
                      onChange={handleChange}
                      placeholder="您的名称或昵称"
                      className={errors.author ? "border-red-500" : ""}
                    />
                    {errors.author && <p className="text-xs text-red-500">{errors.author}</p>}
                  </div> */}

                  <div className="space-y-2">
                    <Label htmlFor="homepage">个人主页</Label>
                    <Input
                      id="homepage"
                      name="homepage"
                      value={formData.homepage}
                      onChange={handleChange}
                      placeholder="https://github.com/yourusername"
                      className={errors.homepage ? "border-red-500" : ""}
                    />
                    {errors.homepage && <p className="text-xs text-red-500">{errors.homepage}</p>}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="summary">
                    摘要 <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="summary"
                    name="summary"
                    value={formData.summary}
                    onChange={handleChange}
                    placeholder="简短概括您的应用案例（100字以内）"
                    className={`min-h-[80px] ${errors.summary ? "border-red-500" : ""}`}
                  />
                  {errors.summary && <p className="text-xs text-red-500">{errors.summary}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="content">
                    正文 <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="content"
                    name="content"
                    value={formData.content}
                    onChange={handleChange}
                    placeholder="详细描述您的MCP应用案例，包括使用的工具、实现的功能等，请使用markdown"
                    className={`min-h-[120px] ${errors.content ? "border-red-500" : ""}`}
                  />
                  {errors.content && <p className="text-xs text-red-500">{errors.content}</p>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="cover">封面图URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="cover"
                        name="cover"
                        value={formData.cover}
                        onChange={handleChange}
                        placeholder="https://example.com/cover.jpg"
                        className={errors.cover ? "border-red-500" : ""}
                      />
                      <div className="relative">
                        <input
                          type="file"
                          id="cover-upload"
                          className="hidden"
                          accept="image/*"
                          onChange={handleFileUpload}
                          disabled={isUploading}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          className="whitespace-nowrap"
                          onClick={() => document.getElementById('cover-upload')?.click()}
                          disabled={isUploading}
                        >
                          {isUploading ? (
                            <span className="flex items-center gap-2">
                              <Upload theme="outline" size="16" className="animate-spin" />
                              上传中...
                            </span>
                          ) : (
                            <span className="flex items-center gap-2">
                              <Upload theme="outline" size="16" />
                              上传图片
                            </span>
                          )}
                        </Button>
                      </div>
                    </div>
                    {errors.cover && <p className="text-xs text-red-500">{errors.cover}</p>}
                  </div>
                </div>

                {/* 视频平台和ID */}
                <div className="space-y-4">
                  <Label>视频平台</Label>
                  <RadioGroup
                    value={formData.videoPlatform}
                    onValueChange={handleVideoPlatformChange}
                    className="flex flex-col space-y-1"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="youtube" id="youtube" />
                      <Label htmlFor="youtube" className="cursor-pointer">
                        YouTube
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="BiliBili" id="BiliBili" />
                      <Label htmlFor="BiliBili" className="cursor-pointer">
                        BiliBili
                      </Label>
                    </div>
                  </RadioGroup>

                  <div className="space-y-2">
                    <Label htmlFor="videoId">
                      视频ID
                      <span className="ml-2 text-xs text-muted-foreground">
                        {formData.videoPlatform === "youtube" ? "(例如: dQw4w9WgXcQ)" : "(例如: 170001)"}
                      </span>
                    </Label>
                    <Input
                      id="videoId"
                      name="videoId"
                      value={formData.videoId}
                      onChange={handleChange}
                      placeholder={formData.videoPlatform === "youtube" ? "输入YouTube视频ID" : "输入BiliBili视频ID"}
                      className={errors.videoId ? "border-red-500" : ""}
                    />
                    {errors.videoId && <p className="text-xs text-red-500">{errors.videoId}</p>}

                    <p className="text-xs text-muted-foreground mt-1">
                      {formData.videoPlatform === "youtube"
                        ? "完整链接将是: https://www.youtube.com/embed/VIDEO_ID"
                        : "完整链接将是: https://player.bilibili.com/player.html?isOutside=true&bvid=VIDEO_ID"}
                    </p>

                    {/* 视频预览区域 */}
                    {previewUrl && (
                      <div className="mt-4 space-y-2">
                        <Label>视频预览</Label>
                        <div className="rounded-lg overflow-hidden aspect-video border">
                          <iframe
                            src={`${previewUrl.includes('?')? previewUrl + '&autoplay=0' : previewUrl + '?autoplay=0'}`}
                            className="w-full h-full"
                            title="视频预览"
                            frameBorder="0"
                            allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                          ></iframe>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 服务器选择器 (多选) */}
                  <div className="space-y-2">
                    <Label htmlFor="serverIds">使用的MCP服务器</Label>
                    <Popover open={serverPopoverOpen} onOpenChange={setServerPopoverOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={serverPopoverOpen}
                          className="w-full justify-between min-h-10"
                        >
                          <div className="flex flex-wrap gap-1 py-1">
                            {selectedServers.length > 0 ? (
                              selectedServers.map((server) => (
                                <Badge
                                  key={server.ID}
                                  variant="secondary"
                                  className="flex items-center gap-1 px-2 py-1"
                                >
                                  {server.name}
                                  <div
                                    onClick={(e) => removeServer(server.ID, e)}
                                    className="ml-1 rounded-full hover:bg-muted cursor-pointer"
                                  >
                                    <Close theme="outline" size="12" />
                                  </div>
                                </Badge>
                              ))
                            ) : (
                              <span className="text-muted-foreground">选择MCP服务器</span>
                            )}
                          </div>
                          <Down theme="outline" size="16" className="opacity-50 shrink-0" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0" align="start">
                        <Command>
                          <CommandInput placeholder="搜索服务器..." className="h-9" />
                          <CommandList>
                            <CommandEmpty>未找到服务器</CommandEmpty>
                            <CommandGroup>
                              {serverData.map((server) => {
                                const isSelected = formData.serverIds.includes(server.ID)
                                return (
                                  <CommandItem
                                    key={server.ID}
                                    value={server.name}
                                    onSelect={() => handleServerSelect(server.ID)}
                                    className={cn("flex items-center gap-2", isSelected ? "bg-primary/10" : "")}
                                  >
                                    <div
                                      className={`w-4 h-4 border rounded-sm flex items-center justify-center ${isSelected ? "bg-primary border-primary" : "border-input"}`}
                                    >
                                      {isSelected && <span className="text-white text-xs">✓</span>}
                                    </div>
                                    <span>{server.name}</span>
                                  </CommandItem>
                                )
                              })}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* 客户端选择器 (单选) */}
                  <div className="space-y-2">
                    <Label htmlFor="clientId">使用的MCP客户端</Label>
                    <Popover open={clientPopoverOpen} onOpenChange={setClientPopoverOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={clientPopoverOpen}
                          className="w-full justify-between h-10"
                        >
                          {selectedClient ? (
                            <span>{selectedClient.name}</span>
                          ) : (
                            <span className="text-muted-foreground">选择MCP客户端</span>
                          )}
                          <Down theme="outline" size="16" className="opacity-50 shrink-0" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0" align="start">
                        <Command>
                          <CommandInput placeholder="搜索客户端..." className="h-9" />
                          <CommandList>
                            <CommandEmpty>未找到客户端</CommandEmpty>
                            <CommandGroup>
                              {clientData.map((client) => {
                                const isSelected = formData.clientId === client.ID
                                return (
                                  <CommandItem
                                    key={client.ID}
                                    value={client.name}
                                    onSelect={() => handleClientSelect(client.ID)}
                                    className="flex items-center justify-between"
                                  >
                                    <span>{client.name}</span>
                                    {isSelected && <Check theme="outline" size="16" className="text-primary" />}
                                  </CommandItem>
                                )
                              })}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </form>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => router.push("/use-cases")}>
                取消
              </Button>
              <Button className="bg-primary hover:bg-primary/90" onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? (
                  <span className="flex items-center gap-2">
                    <Upload theme="outline" size="16" className="animate-spin" />
                    提交中...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <Upload theme="outline" size="16" />
                    提交案例
                  </span>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
      <SiteFooter />
    </>
  )
}
