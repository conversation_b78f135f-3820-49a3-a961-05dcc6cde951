"use client"

import { Suspense } from "react"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { UseCasesClient } from "./use-cases-client"
import { UseCasesSkeleton } from "./use-cases-skeleton"

interface UseCase {
  ID: number
  author: string
  avatar: string
  date: string
  summary: string
  homepage?: string
  video?: string
  cover?: string
}

interface UseCasesPageWrapperProps {
  initialCases: UseCase[]
  initialTotal: number
}

export function UseCasesPageWrapper({ initialCases, initialTotal }: UseCasesPageWrapperProps) {
  return (
    <>
      <SiteHeader />
      <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
        <Suspense fallback={<UseCasesSkeleton />}>
          <UseCasesClient 
            initialCases={initialCases}
            initialTotal={initialTotal}
          />
        </Suspense>
      </div>
      <SiteFooter />
    </>
  )
} 