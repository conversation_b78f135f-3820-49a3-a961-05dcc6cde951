export function UseCasesSkeleton() {
  return (
    <>
      <div className="text-center mb-8">
        <div className="h-10 w-80 bg-muted/30 animate-pulse rounded-md mx-auto mb-2"></div>
        <div className="h-6 w-96 bg-muted/30 animate-pulse rounded-md mx-auto"></div>
      </div>

      {/* 搜索框和投稿按钮骨架 */}
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-8">
        <div className="w-full md:w-96">
          <div className="h-10 w-full bg-muted/30 animate-pulse rounded-md"></div>
        </div>
        <div className="w-full md:w-auto">
          <div className="h-10 w-32 bg-muted/30 animate-pulse rounded-md"></div>
        </div>
      </div>

      {/* 案例列表骨架 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="space-y-3">
            <div className="border rounded-lg p-4 space-y-3">
              {/* 头像和作者信息 */}
              <div className="flex items-start gap-3">
                <div className="w-12 h-12 bg-muted/30 animate-pulse rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-20 bg-muted/30 animate-pulse rounded"></div>
                  <div className="h-3 w-16 bg-muted/30 animate-pulse rounded"></div>
                </div>
              </div>
              
              {/* 内容 */}
              <div className="space-y-2">
                <div className="h-3 w-full bg-muted/30 animate-pulse rounded"></div>
                <div className="h-3 w-4/5 bg-muted/30 animate-pulse rounded"></div>
                <div className="h-3 w-3/5 bg-muted/30 animate-pulse rounded"></div>
              </div>
              
              {/* 媒体内容 */}
              <div className="h-32 w-full bg-muted/30 animate-pulse rounded-lg"></div>
            </div>
          </div>
        ))}
      </div>
    </>
  )
} 