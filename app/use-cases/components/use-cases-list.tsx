"use client"

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LinkTwo } from "@icon-park/react"
import Masonry from "react-masonry-css"

interface UseCase {
  ID: number
  author: string
  avatar: string
  date: string
  summary: string
  homepage?: string
  video?: string
  cover?: string
}

interface UseCasesListProps {
  cases: UseCase[]
  onCaseClick: (id: number) => void
}

export function UseCasesList({ cases, onCaseClick }: UseCasesListProps) {
  // 瀑布流断点配置
  const breakpointColumnsObj = {
    default: 4, // 默认4列
    1400: 3, // 在1400px以下时显示3列
    1100: 2, // 在1100px以下时显示2列
    700: 1, // 在700px以下时显示1列
  }

  // 瀑布流样式
  const masonryStyles = `
.masonry-grid {
  display: flex;
  justify-content: center;
  width: auto;
  margin-left: -16px; /* 调整间距 */
}
.masonry-grid-column {
  padding-left: 16px; /* 调整间距 */
  background-clip: padding-box;
}
`

  if (cases.length === 0) {
    return (
      <div className="col-span-full flex flex-col items-center justify-center min-h-[400px] w-full">
        <div className="flex flex-col items-center justify-center w-full max-w-md mx-auto">
          <div className="w-24 h-24 mb-4 text-muted-foreground">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">暂无应用案例</h3>
          <p className="text-muted-foreground text-center">
            没有找到匹配的应用案例，请尝试其他关键词
          </p>
        </div>
      </div>
    )
  }

  return (
    <>
      <style jsx>{masonryStyles}</style>
      <Masonry breakpointCols={breakpointColumnsObj} className="masonry-grid" columnClassName="masonry-grid-column">
        {cases.map((useCase: any) => (
          <div key={useCase.ID} className="block cursor-pointer" onClick={() => onCaseClick(useCase.ID)}>
            <Card className="card-hover overflow-hidden mb-6">
              <CardHeader className="p-4 pb-0">
                <div className="flex items-start gap-3">
                  <Avatar className="h-12 w-12 border">
                    <AvatarImage src={useCase.avatar} alt={useCase.author} />
                    <AvatarFallback>{useCase.author.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    {useCase.homepage ? (
                      <a
                        href={useCase.homepage}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="font-semibold hover:text-primary transition-colors"
                        onClick={(e) => e.stopPropagation()} // Prevent the card click from triggering
                      >
                        {useCase.author}
                      </a>
                    ) : (
                      <span className="font-semibold">{useCase.author}</span>
                    )}
                    <div className="text-sm text-muted-foreground">{useCase.date}</div>
                  </div>
                  {useCase.homepage && (
                    <a
                      href={useCase.homepage}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 text-xs text-muted-foreground hover:text-primary transition-colors"
                      onClick={(e) => e.stopPropagation()} // Prevent the card click from triggering
                    >
                      <LinkTwo theme="outline" size="20" />
                      <span>主页</span>
                    </a>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-4 pb-6">
                <p className="text-sm pb-2 whitespace-pre-line">{useCase.summary}</p>
                {useCase.cover ? (
                  <div className="rounded-lg overflow-hidden">
                    <img
                      src={useCase.cover || "/placeholder.svg"}
                      alt={`Cover image for ${useCase.author}'s post`}
                      className="w-full h-auto object-cover"
                    />
                  </div>
                ) : useCase.video ? (
                  <div className="rounded-lg overflow-hidden aspect-video relative">
                    <iframe
                      src={`${useCase.video.includes('?')? useCase.video + '&autoplay=0' : useCase.video + '?autoplay=0'}`}
                      className="w-full h-full"
                      title={`Video by ${useCase.author}`}
                      frameBorder="0"
                      allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      onClick={(e) => e.stopPropagation()} // Prevent the card click from triggering
                    ></iframe>
                    <div 
                      className="absolute inset-0 bg-transparent cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        onCaseClick(useCase.ID);
                      }}
                    />
                  </div>
                ) : null}
              </CardContent>
            </Card>
          </div>
        ))}
      </Masonry>
    </>
  )
} 