"use client"

import { useEffect, useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Plus } from "@icon-park/react"
import { useRouter } from "next/navigation"
import { useAuthGuard } from "@/hooks/use-auth-guard"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import useCaseApi from "@/lib/api/apis/useCase"
import { UseCasesList } from "./use-cases-list"

// 每页显示的服务器数量
const ITEMS_PER_PAGE = 12

interface UseCase {
  ID: number
  author: string
  avatar: string
  date: string
  summary: string
  homepage?: string
  video?: string
  cover?: string
}

interface UseCasesClientProps {
  initialCases: UseCase[]
  initialTotal: number
}
let timer: NodeJS.Timeout | undefined = undefined
export function UseCasesClient({ initialCases, initialTotal }: UseCasesClientProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredCases, setFilteredCases] = useState<UseCase[]>(initialCases)
  const { requireAuth } = useAuthGuard()
  const { isAuthenticated, setLoginDialogOpen } = useAuth()
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(Math.ceil(initialTotal / ITEMS_PER_PAGE))
  const isComposing = useRef(false)
  const searchTimeout = useRef<NodeJS.Timeout | undefined>(undefined)
  const [total, setTotal] = useState(initialTotal)
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(Math.ceil(initialTotal / ITEMS_PER_PAGE) > 1)
  const [inputValue, setInputValue] = useState("")
  const [isInitialLoad, setIsInitialLoad] = useState(false)
  const loadingRef = useRef(false)
  const lastScrollTime = useRef(0)
  const [isClient, setIsClient] = useState(false)
  const latestRequestId = useRef(0)

  // 确保组件只在客户端渲染
  useEffect(() => {
    setIsClient(true)
  }, [])

  // 统一的获取列表方法，只用参数，不依赖外部状态
  const getList = async (keyword: string, page: number, append: boolean = false) => {
    const requestId = Date.now() + Math.random()
    latestRequestId.current = requestId
    const params = {
      page,
      pageSize: ITEMS_PER_PAGE,
      keyword,
    }
    const response: any = await useCaseApi.getUseCasePublic(params)
    if (latestRequestId.current !== requestId) return
    if (response && response.data) {
      setTotal(response.data.total)
      setCurrentPage(page)
      if (append) {
        setFilteredCases(prev => [...prev, ...(response.data.list || [])])
      } else {
        setFilteredCases(response.data.list || [])
      }
      const newTotalPages = Math.ceil(response.data.total / ITEMS_PER_PAGE)
      setTotalPages(newTotalPages)
      setHasMore(page < newTotalPages)
    }
  }

  // 处理搜索
  const handleSearch = (value: string) => {
    setCurrentPage(1)
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      getList(value, 1, false)
    }, 200)
  }

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)

    // 如果是英文输入，直接触发搜索
    if (!isComposing.current) {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current)
      }
      searchTimeout.current = setTimeout(() => {
        setSearchQuery(value)
        handleSearch(value)
      }, 500)
    }
  }

  // 处理中文输入法开始
  const handleCompositionStart = () => {
    isComposing.current = true
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current)
    }
  }

  // 处理中文输入法结束
  const handleCompositionEnd = (e: React.CompositionEvent<HTMLInputElement>) => {
    isComposing.current = false
    const value = e.currentTarget.value
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current)
    }
    searchTimeout.current = setTimeout(() => {
      setSearchQuery(value)
      handleSearch(value)
    }, 500)
  }

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current)
      }
    }
  }, [])

  // 处理投稿按钮点击
  const handleSubmitClick = () => {
    if (isAuthenticated) {
      // 如果已登录，直接导航
      router.push("/use-cases/submit")
    } else {
      setLoginDialogOpen(true);
    }
  }

  // 处理案例点击，导航到详情页
  const handleCaseClick = (id: number) => {
    router.push(`/use-cases/${id}`)
  }

  // 处理滚动加载 - 添加更强的防抖保护
  const handleScroll = () => {
    // 检查是否应该加载更多
    if (loadingRef.current || !hasMore || isInitialLoad) {
      return
    }
    
    // 计算滚动位置
    const scrollHeight = document.documentElement.scrollHeight
    const scrollTop = window.scrollY || document.documentElement.scrollTop
    const clientHeight = document.documentElement.clientHeight
    const distanceToBottom = scrollHeight - scrollTop - clientHeight

    // 大幅提前触发加载阈值
    if (distanceToBottom < 500) {
      loadMore()
    }
  }

  // 使用useRef记录上次加载时间，防止频繁调用
  const lastLoadTimeRef = useRef(0)
  const THROTTLE_DELAY = 1000 // 设置1秒的节流延迟

  // 加载更多数据 - 添加时间戳节流
  const loadMore = async () => {
    const now = Date.now()
    if (now - lastLoadTimeRef.current < THROTTLE_DELAY) {
      return
    }
    if (loadingRef.current || !hasMore) {
      return
    }
    lastLoadTimeRef.current = now
    loadingRef.current = true
    setLoading(true)
    const nextPage = currentPage + 1
    try {
      await getList(searchQuery, nextPage, true)
    } catch (error) {
      console.error('加载更多数据失败:', error)
    } finally {
      setLoading(false)
      loadingRef.current = false
    }
  }

  // 添加滚动监听 - 增强防抖机制
  useEffect(() => {
    if (!isClient) return

    // 真正的节流函数
    let scrollThrottleTimeout: NodeJS.Timeout | null = null
    
    const throttledScroll = () => {
      // 如果已经在加载中，直接返回
      if (loadingRef.current) return
      
      // 确保至少有200ms的间隔
      if (Date.now() - lastScrollTime.current < 200) return
      
      // 清除之前的超时
      if (scrollThrottleTimeout) {
        clearTimeout(scrollThrottleTimeout)
      }
      
      // 设置新的超时
      scrollThrottleTimeout = setTimeout(() => {
        lastScrollTime.current = Date.now()
        handleScroll()
      }, 100) // 短暂延迟，减少重复触发
    }
    
    window.addEventListener('scroll', throttledScroll)
    
    // 初始检查是否需要加载更多
    setTimeout(handleScroll, 500)
    
    return () => {
      window.removeEventListener('scroll', throttledScroll)
      if (scrollThrottleTimeout) {
        clearTimeout(scrollThrottleTimeout)
      }
    }
  }, [hasMore, filteredCases.length, searchQuery, isClient])

  // 重置状态
  useEffect(() => {
    if (!isClient) return
    
    setCurrentPage(1)
    setHasMore(true)
    setFilteredCases([])
    getList(searchQuery, 1, false).finally(() => {
      setIsInitialLoad(false)
    })
  }, [searchQuery, isClient])

  // 如果还没有在客户端渲染，显示加载状态
  if (!isClient) {
    return (
      <>
        <div className="text-center mb-8">
          <div className="h-10 w-80 bg-muted/30 animate-pulse rounded-md mx-auto mb-2"></div>
          <div className="h-6 w-96 bg-muted/30 animate-pulse rounded-md mx-auto"></div>
        </div>
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-8">
          <div className="w-full md:w-96">
            <div className="h-10 w-full bg-muted/30 animate-pulse rounded-md"></div>
          </div>
          <div className="w-full md:w-auto">
            <div className="h-10 w-32 bg-muted/30 animate-pulse rounded-md"></div>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-primary mb-2">MCP服务应用案例</h1>
        <p className="text-xl text-muted-foreground">如何应用MCP，使AI不仅具备聪明的大脑，还能实现具体的操作。</p>
      </div>

      {/* 搜索框和投稿按钮 */}
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-8">
        <div className="relative w-full md:w-96">
          <Search
            theme="outline"
            size="16"
            className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
          />
          <Input
            type="text"
            placeholder="搜索应用案例..."
            className="pl-10"
            value={inputValue}
            onChange={handleInputChange}
            onCompositionStart={handleCompositionStart}
            onCompositionEnd={handleCompositionEnd}
          />
        </div>

        {isAuthenticated ? (
          <Link href="/use-cases/submit">
            <Button className="bg-primary hover:bg-primary/90 w-full md:w-auto">
              <Plus theme="outline" size="16" className="mr-2" />
              投稿应用案例
            </Button>
          </Link>
        ) : (
          <Button className="bg-primary hover:bg-primary/90 w-full md:w-auto" onClick={handleSubmitClick}>
            <Plus theme="outline" size="16" className="mr-2" />
            投稿应用案例
          </Button>
        )}
      </div>

      {/* 应用案例列表 */}
      <UseCasesList cases={filteredCases} onCaseClick={handleCaseClick} />

    </>
  )
} 