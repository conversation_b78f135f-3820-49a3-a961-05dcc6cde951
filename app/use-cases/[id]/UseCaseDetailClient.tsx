"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { Left, LinkTwo, Down } from "@icon-park/react"
import Link from "next/link"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function UseCaseDetailClient({ useCase }: { useCase: any }) {
  if (!useCase) {
    return (
      <>
        <SiteHeader />
        <div className="container mx-auto py-12 px-4 gradient-bg min-h-screen">
          <div className="flex justify-center items-center h-64">
            <div className="animate-pulse">
              <div className="h-8 w-64 bg-muted rounded mb-4"></div>
              <div className="h-4 w-96 bg-muted rounded"></div>
            </div>
          </div>
        </div>
        <SiteFooter />
      </>
    )
  }

  return (
    <>
      <SiteHeader />
      <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
        <div className="mb-6">
          <Link href="/use-cases" className="inline-flex items-center text-muted-foreground hover:text-primary">
            <Left theme="outline" size="16" className="mr-1" />
            <span>返回应用案例</span>
          </Link>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="mb-6 flex items-start gap-4">
            <Avatar className="h-16 w-16 border">
              <AvatarImage src={useCase.avatar} alt={useCase.author} />
              <AvatarFallback>{useCase.author.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center gap-3">
                <h1 className="text-2xl font-bold">{useCase.author}</h1>
                {useCase.homepage && (
                  <a
                    href={useCase.homepage}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    <LinkTwo theme="outline" size="16" />
                    <span>主页</span>
                  </a>
                )}
              </div>
              <p className="text-muted-foreground">{useCase.CreatedAt}</p>
            </div>
          </div>

          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">摘要</h2>
                <p className="text-lg leading-relaxed">{useCase.summary}</p>
              </div>

              {/* Media Section */}
              {(useCase.video || useCase.cover) && (
                <div className="mb-8">
                  {useCase.video ? (
                    <div className="rounded-lg overflow-hidden aspect-video">
                      <iframe
                        src={`${useCase.video.includes('?')? useCase.video + '&autoplay=0' : useCase.video + '?autoplay=0'}`}
                        className="w-full h-full"
                        title={`Video by ${useCase.author}`}
                        frameBorder="0"
                        allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      ></iframe>
                    </div>
                  ) : useCase.cover ? (
                    <div className="rounded-lg overflow-hidden">
                      <img
                        src={useCase.cover || "/placeholder.svg"}
                        alt={`Cover image for ${useCase.author}'s post`}
                        className="w-full h-auto object-cover"
                      />
                    </div>
                  ) : null}
                </div>
              )}

              {/* Content Section */}
              <div>
                <h2 className="text-xl font-semibold mb-4">详细内容</h2>
                <div className="prose prose-lg dark:prose-invert max-w-none">
                  <p className="whitespace-pre-line">{useCase.content}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Related Information */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">相关信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {useCase.servers && useCase.servers.length > 0 &&
                (useCase.servers.length === 1 ? (
                  // Single server - show direct link
                  <Link href={`/server/${useCase.servers[0].uuid}`}>
                    <Button variant="outline" className="w-full justify-start">
                      <span className="mr-2">🖥️</span>
                      查看相关MCP服务器: {useCase.servers[0].name}
                    </Button>
                  </Link>
                ) : (
                  // Multiple servers - show dropdown
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between">
                        <div className="flex items-center">
                          <span className="mr-2">🖥️</span>
                          <span>查看相关MCP服务器</span>
                        </div>
                        <Down theme="outline" size="16" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-[200px]">
                      {useCase.servers.map((server: any) => (
                        <DropdownMenuItem key={server.id} asChild>
                          <Link href={`/server/${server.uuid}`} className="cursor-pointer">
                            {server.name}
                          </Link>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ))}

              {useCase.client && (
                <Link href={`/clients?id=${useCase.client.id}`}>
                  <Button variant="outline" className="w-full justify-start">
                    <span className="mr-2">📱</span>
                    查看相关MCP客户端: {useCase.client.name}
                  </Button>
                </Link>
              )}
            </div>
          </div>

          <div className="flex justify-center mt-8">
            <Link href="/use-cases">
              <Button>
                <Left theme="outline" size="16" className="mr-2" />
                返回应用案例列表
              </Button>
            </Link>
          </div>
        </div>
      </div>
      <SiteFooter />
    </>
  )
} 