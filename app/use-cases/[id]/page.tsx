import useCaseApi from "@/lib/api/apis/useCase"
import UseCaseDetailClient from "./UseCaseDetailClient"

// 强制动态渲染 - 确保每次请求时都重新获取数据
export const dynamic = 'force-dynamic'
export const revalidate = 0

// SSR 方式获取数据
async function getDetail(id: string) {
  const response: any = await useCaseApi.findUseCase(id)
  if (response && response.data) {
    return response.data
  }
  return null
}

export default async function UseCaseDetailPage({ params }: { params: { id: string } }) {
  const { id } = await params;
  const useCase = await getDetail(id)
  return <UseCaseDetailClient useCase={useCase} />
}
