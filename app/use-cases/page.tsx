import type React from "react"
import { Suspense } from "react"
import { UseCasesPageWrapper } from "./components/use-cases-page-wrapper"
import { UseCasesSkeleton } from "./components/use-cases-skeleton"
import useCaseApi from "@/lib/api/apis/useCase"

// 强制动态渲染 - 确保每次请求时都重新获取数据
export const dynamic = 'force-dynamic'
export const revalidate = 0

// 每页显示的服务器数量
const ITEMS_PER_PAGE = 12

interface UseCase {
  ID: number
  author: string
  avatar: string
  date: string
  summary: string
  homepage?: string
  video?: string
  cover?: string
}

// 服务端数据获取函数
async function getInitialUseCasesData() {
  try {
    const response: any = await useCaseApi.getUseCasePublic({
      page: 1,
      pageSize: ITEMS_PER_PAGE,
      keyword: "",
    })
    if (!response || !response.data) {
      throw new Error('网络请求失败')
    }
    return {
      initialCases: response.data.list || [],
      totalCount: response.data.total || 0,
    }
  } catch (error) {
    console.error('服务端数据获取失败:', error)
    return {
      initialCases: [],
      totalCount: 0,
    }
  }
}

// 生成页面元数据
export async function generateMetadata() {
  return {
    title: `MCP 应用案例 - 精选适合中国用户的 MCP`,
    description: `探索各种 MCP 应用案例，了解如何在实际项目中使用 MCP 服务器`,
    keywords: 'MCP, 应用案例, 实际应用, 项目案例',
  }
}

// 主页面组件 - 服务端组件
export default async function UseCasesPage() {
  // 在服务端获取初始数据
  const initialData = await getInitialUseCasesData()

  return (
    <Suspense fallback={<UseCasesSkeleton />}>
      <UseCasesPageWrapper 
        initialCases={initialData.initialCases}
        initialTotal={initialData.totalCount}
      />
    </Suspense>
  )
}
