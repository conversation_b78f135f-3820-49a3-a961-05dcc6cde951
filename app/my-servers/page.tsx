"use client"

import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Server, Key } from "@icon-park/react"
import Link from "next/link"

export default function MyServersPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // 如果未登录，重定向到首页
  useEffect(() => {
    // Only redirect if we've finished loading and the user is not authenticated
    if (!isLoading && !isAuthenticated) {
      router.push("/")
    }
  }, [isLoading, isAuthenticated, router])

  if (isLoading) {
    return (
      <>
        <SiteHeader />
        <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
          <div className="flex flex-col items-center justify-center py-12">
            <div className="w-full max-w-md h-12 bg-muted/30 animate-pulse rounded-md mb-4"></div>
            <div className="w-full max-w-md h-64 bg-muted/30 animate-pulse rounded-md"></div>
          </div>
        </div>
        <SiteFooter />
      </>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <>
      <SiteHeader />
      <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
        <div className="grid grid-cols-1 md:grid-cols-[240px_1fr] gap-6">
          {/* 侧边栏导航 */}
          <div className="hidden md:block">
            <div className="space-y-2">
              <Link href="/my-servers">
                <div className="flex items-center gap-2 p-3 rounded-md bg-primary text-primary-foreground">
                  <Server theme="outline" size="18" />
                  <span className="font-medium">我的服务器</span>
                </div>
              </Link>
              {/* <Link href="/api-keys">
                <div className="flex items-center gap-2 p-3 rounded-md hover:bg-muted transition-colors">
                  <Key theme="outline" size="18" />
                  <span className="font-medium">API密钥</span>
                </div>
              </Link> */}
            </div>
          </div>

          {/* 移动端标签页导航 */}
          <div className="md:hidden mb-4">
            <Tabs defaultValue="servers">
              <TabsList className="w-full">
                <TabsTrigger value="servers" className="flex-1">
                  <div className="flex items-center gap-2">
                    <Server theme="outline" size="16" />
                    <span>我的服务器</span>
                  </div>
                </TabsTrigger>
                {/* <TabsTrigger value="keys" className="flex-1">
                  <div className="flex items-center gap-2">
                    <Key theme="outline" size="16" />
                    <span>API密钥</span>
                  </div>
                </TabsTrigger> */}
              </TabsList>
            </Tabs>
          </div>

          {/* 主要内容区域 */}
          <div className="bg-background rounded-lg border shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">我的服务器</h1>
              {/* <Button className="bg-primary hover:bg-primary/90">
                <Plus theme="outline" size="16" className="mr-2" />
                提交服务器
              </Button> */}
            </div>

            <div className="border-t mt-4">
              <div className="grid grid-cols-6 gap-4 py-3 px-2 text-sm font-medium text-muted-foreground">
                <div className="col-span-1">头像</div>
                <div className="col-span-1">名称</div>
                <div className="col-span-1">标题</div>
                <div className="col-span-1">描述</div>
                <div className="col-span-1">状态</div>
                <div className="col-span-1">创建时间</div>
              </div>

              <div className="border-t">
                {/* 无服务器时显示 */}
                <div className="py-12 text-center text-muted-foreground">
                  <p>暂无服务器</p>
                </div>

                {/* 有服务器时显示（示例，实际应该根据数据渲染） */}
                {/* 
                <div className="grid grid-cols-6 gap-4 py-4 px-2 border-b items-center">
                  <div className="col-span-1">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Server Avatar" />
                      <AvatarFallback>SV</AvatarFallback>
                    </Avatar>
                  </div>
                  <div className="col-span-1 font-medium">游戏服务器</div>
                  <div className="col-span-1">Alpha版本</div>
                  <div className="col-span-1 truncate">低延迟高性能游戏服务器</div>
                  <div className="col-span-1">
                    <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
                      在线
                    </Badge>
                  </div>
                  <div className="col-span-1 text-sm text-muted-foreground">2025-03-24</div>
                </div>
                */}
              </div>
            </div>
          </div>
        </div>
      </div>
      <SiteFooter />
    </>
  )
}
