"use client"

import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Plus, Server, Key, Copy, Delete } from "@icon-park/react"
import Link from "next/link"
import apiKey from "@/lib/api/apis/apiKey"
import { ConfirmDialog } from "@/components/confirm-dialog"
// 模拟API密钥数据
type ApiKey = {
  ID: string
  name: string
  createdAt: string
  lastUsed: string | null
  apiKey: string
}

export default function ApiKeysPage() {
  const { isAuthenticated, isLoading, user } = useAuth()
  const router = useRouter()
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([])
  const [showNewKeyForm, setShowNewKeyForm] = useState(false)
  const [newKeyName, setNewKeyName] = useState("")
  const [copiedKeyId, setCopiedKeyId] = useState<string | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [keyToDelete, setKeyToDelete] = useState<string | null>(null)

  const getApiKeys = async () => {
    const response: any = await apiKey.getApiKeys({
      page: 1,
      pageSize: 10,
    })
    setApiKeys(response.data.list)
  }

  useEffect(() => {
    getApiKeys()
  }, [])

  // 如果未登录，重定向到首页
  useEffect(() => {
    // Only redirect if we've finished loading and the user is not authenticated
    if (!isLoading && !isAuthenticated) {
      router.push("/")
    }
  }, [isLoading, isAuthenticated, router])

  // 复制API密钥
  const copyApiKey = (key: string, id: string) => {
    navigator.clipboard.writeText(key)
    setCopiedKeyId(id)
    setTimeout(() => setCopiedKeyId(null), 2000)
  }

  // 创建新API密钥
  const createNewKey = async () => {
    if (!newKeyName.trim()) return
    let response: any = await apiKey.createApiKey({name: newKeyName})
    if (response.code === 0) {
      setApiKeys([...apiKeys, response.data])
      setNewKeyName("")
      setShowNewKeyForm(false)
    }
  }

  // 删除API密钥
  const handleDeleteClick = (id: string) => {
    setKeyToDelete(id)
    setDeleteDialogOpen(true)
  }

  const deleteApiKey = async () => {
    if (!keyToDelete) return
    let response:any = await apiKey.deleteApiKey(keyToDelete);
    if (response.code === 0) {
      setApiKeys(apiKeys.filter((key) => key.ID !== keyToDelete))
    }
    setDeleteDialogOpen(false)
    setKeyToDelete(null)
  }

  if (isLoading) {
    return (
      <>
        <SiteHeader />
        <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
          <div className="flex flex-col items-center justify-center py-12">
            <div className="w-full max-w-md h-12 bg-muted/30 animate-pulse rounded-md mb-4"></div>
            <div className="w-full max-w-md h-64 bg-muted/30 animate-pulse rounded-md"></div>
          </div>
        </div>
        <SiteFooter />
      </>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <>
      <SiteHeader />
      <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
        <div className="grid grid-cols-1 md:grid-cols-[240px_1fr] gap-6">
          {/* 侧边栏导航 */}
          <div className="hidden md:block">
            <div className="space-y-2">
              <Link href="/my-servers">
                <div className="flex items-center gap-2 p-3 rounded-md hover:bg-muted transition-colors">
                  <Server theme="outline" size="18" />
                  <span className="font-medium">我的服务器</span>
                </div>
              </Link>
              {/* <Link href="/api-keys">
                <div className="flex items-center gap-2 p-3 rounded-md bg-primary text-primary-foreground">
                  <Key theme="outline" size="18" />
                  <span className="font-medium">API密钥</span>
                </div>
              </Link> */}
            </div>
          </div>

          {/* 移动端标签页导航 */}
          <div className="md:hidden mb-4">
            <Tabs defaultValue="keys">
              <TabsList className="w-full">
                <TabsTrigger value="servers" className="flex-1" onClick={() => router.push("/my-servers")}>
                  <div className="flex items-center gap-2">
                    <Server theme="outline" size="16" />
                    <span>我的服务器</span>
                  </div>
                </TabsTrigger>
                {/* <TabsTrigger value="keys" className="flex-1">
                  <div className="flex items-center gap-2">
                    <Key theme="outline" size="16" />
                    <span>API密钥</span>
                  </div>
                </TabsTrigger> */}
              </TabsList>
            </Tabs>
          </div>

          {/* 主要内容区域 */}
          <div className="bg-background rounded-lg border shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">API密钥</h1>
              <Button className="bg-primary hover:bg-primary/90" onClick={() => setShowNewKeyForm(true)}>
                <Plus theme="outline" size="16" className="mr-2" />
                创建API密钥
              </Button>
            </div>

            {showNewKeyForm && (
              <div className="mb-6 p-4 border rounded-md bg-muted/30">
                <h3 className="text-lg font-medium mb-3">创建新API密钥</h3>
                <div className="flex gap-3 mb-3">
                  <Input
                    placeholder="密钥名称"
                    value={newKeyName}
                    onChange={(e) => setNewKeyName(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={createNewKey}>创建</Button>
                  <Button variant="outline" onClick={() => setShowNewKeyForm(false)}>
                    取消
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  MCP客户端程序可通过API密钥调用在线MCP服务，会消耗您的调用次数，请妥善保管密钥，不要告诉任何人。
                </p>
              </div>
            )}

            <div className="border-t mt-4">
              <div className="grid grid-cols-12 gap-4 py-3 px-2 text-sm font-medium text-muted-foreground">
                <div className="col-span-3">名称</div>
                <div className="col-span-5">密钥</div>
                <div className="col-span-2">创建时间</div>
                <div className="col-span-2">操作</div>
              </div>

              <div className="border-t">
                {apiKeys.length === 0 ? (
                  <div className="py-12 text-center text-muted-foreground">
                    <p>暂无API密钥</p>
                  </div>
                ) : (
                  apiKeys.map((apiKey) => (
                    <div key={apiKey.ID} className="grid grid-cols-12 gap-4 py-4 px-2 border-b items-center">
                      <div className="col-span-3 font-medium">{apiKey.name}</div>
                      <div className="col-span-5 font-mono text-sm truncate">
                        {apiKey.apiKey.substring(0, 10)}...{apiKey.apiKey.substring(apiKey.apiKey.length - 5)}
                      </div>
                      <div className="col-span-2 text-sm text-muted-foreground">{apiKey.createdAt}</div>
                      <div className="col-span-2 flex gap-2">
                        {copiedKeyId === apiKey.ID ? (
                          <div className="h-8 w-10 flex items-center justify-center">
                            <span className="text-xs text-green-500">已复制</span>
                          </div>
                        ) : (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => copyApiKey(apiKey.apiKey, apiKey.ID)}
                            aria-label="复制API密钥"
                          >
                            <div className="group relative">
                              <Copy theme="outline" size="16" />
                              <span className="absolute -top-8 left-1/2 -translate-x-1/2 w-16 bg-black/80 text-white text-xs rounded py-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                复制
                              </span>
                            </div>
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
                          onClick={() => handleDeleteClick(apiKey.ID)}
                          aria-label="删除API密钥"
                        >
                          <div className="group relative">
                            <Delete theme="outline" size="16" />
                            <span className="absolute -top-8 left-1/2 -translate-x-1/2 w-16 bg-black/80 text-white text-xs rounded py-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              删除
                            </span>
                          </div>
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <SiteFooter />

      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="确认删除API密钥"
        description="此操作无法撤销。删除后，该API密钥将无法再使用。"
        onConfirm={deleteApiKey}
        confirmText="删除"
        confirmButtonClassName="bg-red-500 hover:bg-red-600"
      />
    </>
  )
}
