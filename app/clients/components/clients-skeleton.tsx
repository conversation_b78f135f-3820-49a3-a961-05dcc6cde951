"use client"

import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card"

export function ClientsSkeleton() {
  return (
    <>
      <SiteHeader />
      <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
        <div className="mb-8">
          <div className="h-8 w-64 bg-muted/30 animate-pulse rounded-md mb-2"></div>
          <div className="h-4 w-full max-w-2xl bg-muted/30 animate-pulse rounded-md"></div>
        </div>

        <div className="flex flex-col md:flex-row gap-6 mb-8">
          <div className="w-full md:w-2/5">
            <div className="h-12 w-full bg-muted/30 animate-pulse rounded-md"></div>
          </div>
        </div>

        {/* 客户端列表骨架 */}
        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="shadow-sm">
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-muted/30 animate-pulse rounded-full"></div>
                    <div>
                      <div className="h-5 w-32 bg-muted/30 animate-pulse rounded-md"></div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-2 mb-3">
                  <div className="h-4 w-full bg-muted/30 animate-pulse rounded-md"></div>
                  <div className="h-4 w-3/4 bg-muted/30 animate-pulse rounded-md"></div>
                </div>
              </CardContent>
              <CardFooter className="pt-2">
                <div className="h-10 w-full bg-muted/30 animate-pulse rounded-md"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
        
      </div>
      <SiteFooter />
    </>
  )
} 