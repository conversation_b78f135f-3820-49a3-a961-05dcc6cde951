"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { DownloadOne } from "@icon-park/react"
import { useMediaQuery } from "@/hooks/use-media-query"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { Pagination } from "@/components/pagination"
import sysDictionaryDetailApi from "@/lib/api/apis/sysDictionaryDetail"

// 每页显示的客户端数量
const ITEMS_PER_PAGE = 12

type ClientsPageWrapperProps = {
  initialClients: any[]
  initialTotalCount: number
  initialCategories: any[]
}

export function ClientsPageWrapper({ 
  initialClients, 
  initialTotalCount, 
  initialCategories 
}: ClientsPageWrapperProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(Math.ceil(initialTotalCount / ITEMS_PER_PAGE))
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [filteredClients, setFilteredClients] = useState(initialClients)
  const [sysDictionaryList, setSysDictionaryList] = useState(initialCategories)

  // 检测是否为移动设备
  const isMobile = useMediaQuery("(max-width: 768px)")

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category)
    setCurrentPage(1)
  }
  
  const fetchMcpClientPublic = async () => {
    const response: any = await sysDictionaryDetailApi.getMcpClientPublic({
      category: selectedCategory === "all" ? "" : selectedCategory,
      page: currentPage,
      pageSize: ITEMS_PER_PAGE
    })
    setTotalPages(Math.ceil(response.data.total / ITEMS_PER_PAGE));
    setFilteredClients(response.data.list)
  }

  useEffect(() => {
    // 如果不是初始状态，则重新获取数据
    fetchMcpClientPublic()
  }, [selectedCategory, currentPage])

  // 页码导航
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)))
  }

  return (
    <>
      <SiteHeader />
      <div className="container mx-auto py-6 px-4 gradient-bg min-h-screen">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-primary mb-2">什么是MCP客户端</h1>
          <p className="text-muted-foreground">
            指能够安装MCP服务并提供AI对话功能的软件，在MCP客户端中安装某MCP服务，对话过程中便可让AI使用其所提供的工具或能力（如可实现让Deepseek等AI大模型具备操作电脑的能力）
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-6 mb-8">
          {/* 类别筛选 */}
          <div className="w-full md:w-2/5">
            <Tabs defaultValue="all" onValueChange={handleCategorySelect}>
              <TabsList className="w-full mb-4 bg-muted border border-border p-1">
                <TabsTrigger
                  value="all"
                  className="flex-1 flex items-center justify-center gap-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                >
                  全部
                </TabsTrigger>
                {sysDictionaryList.map((item: any) => (
                  <TabsTrigger
                    key={item.ID}
                    value={item.value}
                    className="flex-1 flex items-center justify-center gap-1 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=inactive]:bg-transparent data-[state=inactive]:text-foreground"
                  >
                    {item.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>
        </div>

        {/* 客户端列表 */}
        {filteredClients.length > 0 ? (
          <>
            <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {filteredClients.map((client: any) => (
                <Card key={client.ID} className="card-hover shadow-sm hover:shadow-md transition-shadow duration-200">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <img className="text-3xl" style={{width: "35px", height: "35px", borderRadius: "50%"}} src={client.logo}></img>
                      <div>
                        <CardTitle className="text-xl">{client.name}</CardTitle>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <p className="text-sm mb-3" style={{minHeight: "40px"}}>{client.description}</p>
                  <div className="flex flex-wrap gap-1 mb-3"></div>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground" onClick={() => window.open(client.downloadUrl, '_blank')}>
                    <DownloadOne theme="outline" size="16" className="mr-2" />
                    前往官网下载
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
          {/* 分页控件 */}
          <div className="flex justify-center mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              goToPage={goToPage}
              isMobile={isMobile}
            />
          </div>
          </>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold mb-2">未找到客户端软件</h3>
            <p className="text-muted-foreground">请尝试调整您的搜索或筛选条件</p>
          </div>
        )}
        
      </div>
      <SiteFooter />
    </>
  )
} 