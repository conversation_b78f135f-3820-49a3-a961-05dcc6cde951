import type React from "react"
import { Suspense } from "react"
import { ClientsPageWrapper } from "./components/clients-page-wrapper"
import { ClientsSkeleton } from "./components/clients-skeleton"
import sysDictionaryDetailApi from "@/lib/api/apis/sysDictionaryDetail"

// 强制动态渲染 - 确保每次请求时都重新获取数据
export const dynamic = 'force-dynamic'
export const revalidate = 0

// 服务端数据获取函数
async function getClientsData() {
  try {
    // 获取客户端数据和分类数据
    const [clientsResponse, categoriesResponse] = await Promise.all([
      sysDictionaryDetailApi.getMcpClientPublic({
        category: "",
        page: 1,
        pageSize: 12
      }),
      sysDictionaryDetailApi.GetSysDictionaryList({
        page: 1,
        pageSize: 20,
        sysDictionaryID: 8
      })
    ])

    const clientsData = (clientsResponse && typeof clientsResponse === 'object' && 'data' in clientsResponse) ? clientsResponse : { data: { list: [], total: 0 } }
    const categoriesData = (categoriesResponse && typeof categoriesResponse === 'object' && 'data' in categoriesResponse) ? categoriesResponse : { data: { list: [], total: 0 } }

    return {
      initialClients: (clientsData.data as any)?.list || [],
      totalCount: (clientsData.data as any)?.total || 0,
      categories: (categoriesData.data as any)?.list || [],
    }
  } catch (error) {
    console.error('服务端数据获取失败:', error)
    
    return {
      initialClients: [],
      totalCount: 0,
      categories: [],
    }
  }
}

// 生成页面元数据
export async function generateMetadata() {
  return {
    title: `MCP 客户端导航`,
    description: `查找并筛选最新的MCP客户端软件，能够安装MCP服务并提供AI对话功能的软件，在MCP客户端中安装某MCP服务，对话过程中便可让AI使用其所提供的工具或能力（如可实现让Deepseek等AI大模型具备操作电脑的能力）`,
    keywords: 'MCP, 服务器, 客户端, 应用案例, 在线调用MCP服务器',
    openGraph: {
      title: 'MCP 客户端导航',
      description: `查找并筛选最新的MCP客户端软件，能够安装MCP服务并提供AI对话功能的软件，在MCP客户端中安装某MCP服务，对话过程中便可让AI使用其所提供的工具或能力（如可实现让Deepseek等AI大模型具备操作电脑的能力）`,
      type: 'website',
      locale: 'zh_CN',
    }
  }
}

// 主页面组件 - 服务端组件
export default async function ClientsPage() {
  // 在服务端获取初始数据
  const initialData = await getClientsData()

  return (
    <Suspense fallback={<ClientsSkeleton />}>
      <ClientsPageWrapper 
        initialClients={initialData.initialClients}
        initialTotalCount={initialData.totalCount}
        initialCategories={initialData.categories}
      />
    </Suspense>
  )
}
