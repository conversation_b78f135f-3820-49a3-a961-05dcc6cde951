export type ClientSoftware = {
  ID: string
  id: number
  name: string
  description: string
  version: string
  releaseDate: string
  size: string
  platform: string[]
  category: string
  downloadCount: number
  features: string[]
  requirements: string
  developer: string
  logo: string
}

export const clientsData: ClientSoftware[] = [
  {
    ID: '1',
    id: 1,
    name: "MCP Studio",
    description: "专业的MCP开发环境，支持多种模型和协议",
    version: "2.5.1",
    releaseDate: "2025-03-20",
    size: "145MB",
    platform: ["Windows", "macOS", "Linux"],
    category: "development",
    downloadCount: 12458,
    features: ["支持多种大型语言模型", "内置调试工具", "可视化编辑器", "代码自动补全", "性能分析工具"],
    requirements: "8GB RAM, 4核CPU, 2GB存储空间",
    developer: "MCP开发团队",
    logo: "🧩",
  },
  {
    ID: '2',
    id: 2,
    name: "MCP Connect",
    description: "轻量级MCP客户端，快速连接到任何MCP服务器",
    version: "1.8.3",
    releaseDate: "2025-03-15",
    size: "32MB",
    platform: ["Windows", "macOS", "Linux", "Android", "iOS"],
    category: "aitool",
    downloadCount: 45672,
    features: ["快速连接到MCP服务器", "支持多种认证方式", "历史记录管理", "低延迟通信", "自动重连功能"],
    requirements: "2GB RAM, 2核CPU, 100MB存储空间",
    developer: "连接科技",
    logo: "🔌",
  },
  {
    ID: '3',
    id: 3,
    name: "MCP Visualizer",
    description: "MCP数据可视化工具，将复杂数据转化为直观图表",
    version: "3.2.0",
    releaseDate: "2025-03-10",
    size: "78MB",
    platform: ["Windows", "macOS"],
    category: "efficiency",
    downloadCount: 8934,
    features: ["实时数据可视化", "多种图表类型", "自定义仪表盘", "数据导出功能", "报告生成器"],
    requirements: "4GB RAM, 4核CPU, 500MB存储空间",
    developer: "数据视觉工作室",
    logo: "📊",
  },
  {
    ID: '4',
    id: 4,
    name: "MCP Commander",
    description: "强大的命令行工具，用于管理和部署MCP服务",
    version: "2.0.5",
    releaseDate: "2025-03-05",
    size: "18MB",
    platform: ["Windows", "macOS", "Linux"],
    category: "development",
    downloadCount: 15789,
    features: ["批处理命令执行", "远程服务管理", "自动化脚本支持", "性能监控", "日志分析工具"],
    requirements: "2GB RAM, 2核CPU, 50MB存储空间",
    developer: "命令行工具公司",
    logo: "⌨️",
  },
  {
    ID: '5',
    id: 5,
    name: "MCP Explorer",
    description: "探索和测试MCP API的图形化界面工具",
    version: "1.5.2",
    releaseDate: "2025-03-01",
    size: "65MB",
    platform: ["Windows", "macOS", "Linux"],
    category: "aitool",
    downloadCount: 23456,
    features: ["API请求构建器", "响应分析器", "请求历史记录", "环境变量管理", "团队协作功能"],
    requirements: "4GB RAM, 2核CPU, 200MB存储空间",
    developer: "API工具开发团队",
    logo: "🔍",
  },
  {
    ID: '6',
    id: 6,
    name: "MCP Sync",
    description: "数据同步工具，在多个MCP服务器之间保持数据一致性",
    version: "2.3.1",
    releaseDate: "2025-02-25",
    size: "42MB",
    platform: ["Windows", "macOS", "Linux"],
    category: "aitool",
    downloadCount: 7823,
    features: ["实时数据同步", "冲突解决机制", "增量同步", "加密传输", "同步历史记录"],
    requirements: "4GB RAM, 2核CPU, 100MB存储空间",
    developer: "同步技术有限公司",
    logo: "🔄",
  },
  {
    ID: '7',
    id: 7,
    name: "MCP Security Scanner",
    description: "MCP服务安全扫描工具，检测潜在漏洞和安全问题",
    version: "3.0.0",
    releaseDate: "2025-02-20",
    size: "56MB",
    platform: ["Windows", "macOS", "Linux"],
    category: "security",
    downloadCount: 9876,
    features: ["漏洞扫描", "安全配置检查", "渗透测试工具", "合规性报告", "自动修���建议"],
    requirements: "4GB RAM, 4核CPU, 200MB存储空间",
    developer: "安全防护科技",
    logo: "🔒",
  },
  {
    ID: '8',
    id: 8,
    name: "MCP Dashboard",
    description: "综合监控仪表盘，实时监控MCP服务器状态和性能",
    version: "2.7.3",
    releaseDate: "2025-02-15",
    size: "85MB",
    platform: ["Windows", "macOS", "Linux", "Web"],
    category: "monitoring",
    downloadCount: 18765,
    features: ["实时性能监控", "资源使用分析", "警报系统", "历史数据查询", "自定义视图"],
    requirements: "4GB RAM, 2核CPU, 300MB存储空间",
    developer: "监控解决方案公司",
    logo: "📈",
  },
]

export const categoryTranslations = {
  all: "全部",
  development: "开发工具",
  aitool: "AI聊天工具",
  efficiency: "效率工具",
  // security: "安全工具",
  // monitoring: "监控工具",
}
