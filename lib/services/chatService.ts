import { modelService } from '@/lib/api/model';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  tool_calls?: Array<{
    index: number;
    id: string;
    type: string;
    function: {
      name: string;
      arguments: string;
    };
  }>;
}

export interface ChatTool {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

export interface ChatOptions {
  messages: ChatMessage[];
  tools?: ChatTool[];
}

export interface ChatCallbacks {
  onUpdate?: (content: string, delta: string, toolCalls?: any[], isNewMessage?: boolean) => void;
  onFinish?: (content: string, toolCalls?: any[]) => void;
  onError?: (error: any) => void;
  onThinking?: (thinking: boolean, time?: number) => void;
}

export interface ToolCallResult {
  tool: string;
  args: any;
  result: any;
}

export class ChatService {
  private static instance: ChatService;
  
  private constructor() {}
  
  public static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  /**
   * 发送聊天消息并获取响应
   */
  public async chat(options: ChatOptions, callbacks: ChatCallbacks = {}) {
    try {
      const validMessages = options.messages.map(msg => ({
        role: msg.role,
        content: msg.content || ''
      }));

      return modelService.chat({
        messages: validMessages,
        tools: options.tools,
      }, callbacks);
    } catch (error) {
      callbacks.onError?.(error);
      throw error;
    }
  }

  /**
   * 处理工具调用
   * @param toolCallHandler 用于执行实际工具调用的函数
   * @param toolCalls 来自模型的工具调用请求
   * @param messages 当前的消息列表
   * @param callbacks 回调函数
   */
  public async handleToolCalls(
    toolCallHandler: (toolName: string, args: Record<string, any>) => Promise<any>,
    toolCalls: any[],
    messages: ChatMessage[],
    callbacks: ChatCallbacks = {}
  ) {
    if (!toolCalls || toolCalls.length === 0) {
      return [];
    }

    let toolResults: ToolCallResult[] = [];
    
    try {
      // 执行工具调用
      for (const toolCall of toolCalls) {
        const args = JSON.parse(toolCall.function.arguments);
        const toolResponse = await toolCallHandler(toolCall.function.name, args);
        toolResults.push({
          tool: toolCall.function.name,
          args: args,
          result: toolResponse
        });
      }
    } catch (error: any) {
      console.error('工具调用错误:', error);
      toolResults.push({
        tool: 'error',
        args: {},
        result: { error: error.message }
      });
    }

    // 如果有工具调用结果，发送给模型进行格式化
    if (toolResults.length > 0) {
      const formatMessage = {
        role: 'user' as const,
        content: `请帮我整理以下工具调用的结果，以更易读的方式展示：\n${JSON.stringify(toolResults, null, 2)}`
      };

      try {
        // 调用模型格式化工具调用结果
        await this.chat({
          messages: [...messages, formatMessage],
        }, {
          onUpdate: callbacks.onUpdate,
          onFinish: callbacks.onFinish,
          onError: (error) => {
            console.error('格式化错误:', error);
            // 如果格式化失败，使用原始内容
            callbacks.onFinish?.('工具调用结果：\n' + JSON.stringify(toolResults, null, 2), []);
          }
        });
      } catch (error) {
        console.error('格式化请求失败:', error);
        // 如果请求失败，使用原始内容
        callbacks.onFinish?.('工具调用结果：\n' + JSON.stringify(toolResults, null, 2), []);
      }
    }

    return toolResults;
  }
}

// 导出单例实例
export const chatService = ChatService.getInstance(); 