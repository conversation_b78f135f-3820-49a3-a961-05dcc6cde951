import { Home, Computer, Book, Workbench, People, ShoppingCart, Wallet, Setting, MoreApp } from "@icon-park/react"

// 修改 Tool 类型定义，添加 points 字段
// 在 serverData 数组前添加以下类型定义
type Tool = {
  name: string
  description: string
  points: number // 添加积分消耗字段
  inputSchema: {
    type: string
    properties: Record<string, any>
    required?: string[]
  }
}

// 服务器类型定义
type ServerType = {
  id: number //id，自动生成，无需填写
  package: string //包名，需填写（必填）
  name: string //名称，需填写（必填）
  description: string //一句话功能描述，需填写（必填）
  category: string //分类，下拉选择（必填）
  tags: string[] //标签，可添加多个，添加时提供可搜索的下拉选择用来选择已有tag，没有合适的可新增tag（非必填）
  updatedAt: string //更新时间，自动记录，无需填写
  callMethod: string[] //运行方式，无需填写
  owner: string //作者，需填写（必填）
  onlineUsageCount: number //在线调用次数，自动计数，无需填写
  offlineUsageCount: number //本地安装次数，自动计数，无需填写
  likes: number //点赞次数，自动计数，无需填写
  github: string | null //github地址，需要填写（非必填）
  website: string | null //官网地址，需要填写（非必填）
  detail: string //详细功能说明，需要填写（必填）
  tools: Tool[] //工具数组，无需填写
  logo: string // logo或作者头像，填写url（非必填）
  type: string //程序类型，选择node or python（必填）
}

// 服务器数据
export const serverData = [
  {
    id: 1,
    package: "wonderwhy-er/111",
    name: "游戏服务器 Alpha",
    description: "低延迟高性能游戏服务器",
    category: "computer",
    tags: ["learning", "competitive", "high-capacity"],
    updatedAt: "2025-03-24 14:30:45",
    owner: "GameHost公司",
    onlineUsageCount: 1287,
    offlineUsageCount: 548,
    likes: 342, // 添加点赞数
    github: "https://github.com/gamehost/alpha-server",
    website: "https://alpha-server.gamehost.com",
    logo: "/placeholder.svg?height=60&width=60&text=GS",
    type: "node",
    detail: `
# 游戏服务器 Alpha

## 概述
游戏服务器 Alpha 是一款专为高性能游戏体验设计的MCP服务器，提供低延迟、高稳定性的游戏环境。

## 技术特点
- **低延迟架构**：优化的网络栈，确保游戏数据传输的最小延迟
- **高并发支持**：可同时处理数百名玩家的连接和游戏状态
- **自动扩展**：根据玩家数量自动调整服务器资源
- **实时监控**：提供游戏服务器性能和玩家活动的实时监控

## 适用场景
- 电子竞技比赛
- 大型多人在线游戏
- 需要低延迟的快节奏游戏
- 游戏直播和录制

## 系统要求
- 推荐带宽：100Mbps以上
- 支持的游戏引擎：Unity、Unreal Engine、自定义引擎
- 兼容性：支持大多现代游戏协议
    `,
    tools: [
      {
        name: "game_status",
        description: "获取游戏服务器当前状态信息",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            server_id: { type: "string", description: "服务器ID" },
            detail_level: {
              type: "string",
              description: "详情级别",
              enum: ["basic", "detailed", "full"],
            },
          },
          required: ["server_id"],
        },
      },
      {
        name: "player_stats",
        description: "查询玩家游戏数据统计",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            player_id: { type: "string", description: "玩家ID" },
            time_range: {
              type: "string",
              description: "时间范围",
              enum: ["daily", "weekly", "monthly", "all"],
            },
            stats_type: {
              type: "array",
              description: "统计类型",
              items: {
                enum: ["kills", "deaths", "wins", "playtime"],
              },
            },
          },
          required: ["player_id", "stats_type"],
        },
      },
    ],
    callMethod: ["local", "online"],
  },
  {
    id: 2,
    package: "wonderwhy-er/222",
    name: "数据处理服务器",
    description: "针对重型数据处理工作负载优化",
    category: "business",
    tags: ["analytics", "big-data", "learning"],
    updatedAt: "2025-03-25 09:15:22",
    owner: "数据科技有限公司",
    onlineUsageCount: 856,
    offlineUsageCount: 324,
    likes: 189, // 添加点赞数
    github: "https://github.com/datatech/data-processor",
    website: "https://datatech.io/processor",
    logo: "/placeholder.svg?height=60&width=60&text=DP",
    type: "node",
    detail: `
# 数据处理服务器

## 概述
数据处理服务器是专为大规模数据分析和处理设计的高性能MCP服务器，能够处理TB级别的数据集。

## 核心功能
- **分布式计算**：支持跨多个节点的并行数据处理
- **内存优化**：针对大数据集的内存使用进行了特殊优化
- **数据转换**：提供多种数据格式之间的无缝转换
- **高级分析**：内置多种统计和机器学习算法

## 适用场景
- 大数据分析项目
- 科学计算和研究
- 商业智能应用
- 实时数据处理管道

## 集成能力
- 支持与Hadoop、Spark等大数据框架集成
- 提供与常见数据库系统的连接器
- 可与云存储服务无缝协作
    `,
    tools: [
      {
        name: "calculate_sum",
        description: "计算数字总和",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            a: { type: "number", description: "参数a" },
            b: { type: "number", description: "参数b" },
            operations: {
              type: "array",
              items: {
                enum: ["sum", "average", "count"],
              },
              description: "操作类型",
            },
          },
          required: ["a", "b", "operations"],
        },
      },
      {
        name: "data_transform",
        description: "数据格式转换工具",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            input_format: {
              type: "string",
              description: "输入格式",
              enum: ["json", "csv", "xml", "yaml"],
            },
            output_format: {
              type: "string",
              description: "输出格式",
              enum: ["json", "csv", "xml", "yaml"],
            },
            data: { type: "string", description: "要转换的数据" },
          },
          required: ["input_format", "output_format", "data"],
        },
      },
    ],
    callMethod: ["local", "online"],
  },
  {
    id: 3,
    package: "wonderwhy-er/destop-commander",
    name: "网站托管服务器",
    description: "可靠的网站和Web应用程序托管",
    category: "ecommerce",
    tags: ["web", "reliable", "scalable"],
    updatedAt: "2025-03-23 18:45:10",
    owner: "云托管服务",
    onlineUsageCount: 2145,
    offlineUsageCount: 876,
    likes: 567, // 添加点赞数
    github: "https://github.com/cloudhosting/webserver",
    website: "https://cloudhosting.com",
    logo: "/placeholder.svg?height=60&width=60&text=WH",
    type: "python",
    detail: `
# 网站托管服务器

## 概述
网站托管服务器提供高可用性的Web应用程序托管环境，适合从小型网站到大型电子商平台的各种应用。

## 主要特性
- **自动扩展**：根据流量自动调整资源分配
- **负载均衡**：智能分配请求以确保最佳性能
- **CDN集成**：与全球内容分发网络无缝集成
- **SSL管理**：自动化的SSL证书管理和更新

## 适用场景
- 企业网站
- 电子商务平台
- 内容管理系统
- Web应用程序

## 技术栈支持
- 支持PHP、Node.js、Python、Ruby等多种语言
- 兼容MySQL、PostgreSQL、MongoDB等数据库
- 提供Redis、Memcached等缓存服务
    `,
    tools: [
      {
        name: "site_status",
        description: "检查网站状态和性能",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            url: { type: "string", description: "网站URL" },
            check_type: {
              type: "array",
              description: "检查类型",
              items: {
                enum: ["uptime", "response_time", "ssl", "dns"],
              },
            },
          },
          required: ["url", "check_type"],
        },
      },
      {
        name: "deploy_site",
        description: "部署网站更新",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            repo_url: { type: "string", description: "代码仓库URL" },
            branch: { type: "string", description: "分支名称" },
            environment: {
              type: "string",
              description: "部署环境",
              enum: ["development", "staging", "production"],
            },
          },
          required: ["repo_url", "branch", "environment"],
        },
      },
    ],
    callMethod: ["online"],
  },
  {
    id: 4,
    package: "wonderwhy-er/destop-commander",
    name: "媒体流服务器",
    description: "针对视频和音频流优化",
    category: "social",
    tags: ["streaming", "high-bandwidth", "low-latency"],
    updatedAt: "2025-03-25 11:20:35",
    owner: "流媒体科技",
    onlineUsageCount: 3421,
    offlineUsageCount: 1245,
    likes: 721, // 添加点赞数
    github: "https://github.com/streamtech/mediaserver",
    website: "https://streamtech.io",
    logo: "/placeholder.svg?height=60&width=60&text=MS",
    type: "node",
    detail: `
# 媒体流服务器

## 概述
媒体流服务器专为高质量视频和音频流传输而设计，提供低延迟、高带宽的流媒体解决方案。

## 核心功能
- **实时转码**：支持多种格式和分辨率的实时视频转码
- **自适应比特率**：根据用户网络条件自动调整流质量
- **全球分发**：通过全球节点网络确保低延迟传输
- **DRM支持**：集成数字版权管理系统

## 适用场景
- 直播平台
- 视频点播服务
- 在线教育
- 远程会议系统

## 技术规格
- 支持的协议：RTMP、HLS、DASH、WebRTC
- 视频编解码器：H.264、H.265、VP9、AV1
- 音频编解码器：AAC、Opus、MP3
    `,
    tools: [
      {
        name: "stream_analytics",
        description: "获取流媒体分析数据",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            stream_id: { type: "string", description: "流ID" },
            metrics: {
              type: "array",
              description: "指标类型",
              items: {
                enum: ["viewers", "bandwidth", "quality", "engagement"],
              },
            },
            time_period: {
              type: "string",
              description: "时间段",
              enum: ["last_hour", "today", "last_week", "custom"],
            },
            custom_range: {
              type: "object",
              description: "自定义时间范围",
              properties: {
                start: { type: "string", description: "开始时间" },
                end: { type: "string", description: "结束时间" },
              },
            },
          },
          required: ["stream_id", "metrics", "time_period"],
        },
      },
    ],
    callMethod: ["local", "online"],
  },
  {
    id: 5,
    package: "wonderwhy-er/destop-commander",
    name: "数据库服务器集群",
    description: "高可用性数据库服务器集群",
    category: "finance",
    tags: ["database", "high-availability", "learning"],
    updatedAt: "2025-03-22 16:05:50",
    owner: "数据存储解决方",
    onlineUsageCount: 1654,
    offlineUsageCount: 732,
    likes: 412, // 添加点赞数
    github: "https://github.com/datastorage/dbcluster",
    website: "https://datastorage.solutions",
    logo: "/placeholder.svg?height=60&width=60&text=DB",
    type: "python",
    detail: `
# 数据库服务器���群

## 概述
数据库服务器集群提供高可用性、高可靠性的数据存储和管理解决方案，特别适合金融和关键业务应用。

## 核心特性
- **自动故障转移**：在节点故障时无缝切换，确保服务连续性
- **数据复制**：实时数据复制到多个节点，防止数据丢失
- **分片技术**：通过水平分片支持超大规模数据集
- **事务一致性**：即使在分布式环境中也保证ACID属性

## 适用场景
- 金融交易系统
- 电子商务平台
- 医疗记录管理
- 企业资源规划系统

## 技术细节
- 支持SQL和NoSQL数据库引擎
- 提供高级监控和警报系统
- 自动备份和时间点恢复
    `,
    tools: [
      {
        name: "query_executor",
        description: "执行数据库查询",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            database: {
              type: "string",
              description: "数据库类型",
              enum: ["mysql", "postgresql", "mongodb", "redis"],
            },
            query: { type: "string", description: "查询语句" },
            params: {
              type: "array",
              description: "查询参数",
              items: { type: "string" },
            },
          },
          required: ["database", "query"],
        },
      },
      {
        name: "backup_restore",
        description: "数据库备份与恢复",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            operation: {
              type: "string",
              description: "操作类型",
              enum: ["backup", "restore"],
            },
            database_name: { type: "string", description: "数据库名称" },
            backup_id: { type: "string", description: "备份ID (仅恢复时需要)" },
          },
          required: ["operation", "database_name"],
        },
      },
    ],
    callMethod: ["local"],
  },
  {
    id: 6,
    package: "wonderwhy-er/destop-commander",
    name: "开发测试服务器",
    description: "用于测试开发版本的服务器",
    category: "development",
    tags: ["testing", "staging", "development"],
    updatedAt: "2025-03-24 10:30:15",
    owner: "开发团队",
    onlineUsageCount: 347,
    offlineUsageCount: 215,
    likes: 98, // 添加点赞数
    github: "https://github.com/devteam/testserver",
    website: null,
    logo: "/placeholder.svg?height=60&width=60&text=DT",
    type: "node",
    detail: `
# 开发测试服务器

## 概述
开发测试服务器为软件开发团队提供专用的测试环境，支持持续集成和持续部署流程。

## 主要功能
- **环境隔离**：提供与生产环境隔离的测试空间
- **自动化测试**：集成自动化测试框架和工具
- **版本控制**：与Git等版本控制系统深度集成
- **部署管道**：支持CI/CD流程的自动化部署

## 适用场景
- 软件开发团队
- QA测试团队
- DevOps实践
- 敏捷开发流程

## 技术支持
- 支持多种编程语言和框架
- 提供测试报告和覆盖率分析
- 集成问题跟踪系统
    `,
    tools: [
      {
        name: "run_tests",
        description: "运行自动化测试套件",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            test_suite: { type: "string", description: "测试套件名称" },
            environment: {
              type: "string",
              description: "测试环境",
              enum: ["local", "dev", "staging"],
            },
            tags: {
              type: "array",
              description: "测试���签",
              items: { type: "string" },
            },
          },
          required: ["test_suite", "environment"],
        },
      },
    ],
    callMethod: ["local"],
  },
  {
    id: 7,
    package: "wonderwhy-er/destop-commander",
    name: "生活助手服务器",
    description: "提供日常生活服务和便捷工具",
    category: "life",
    tags: ["assistant", "daily", "tools"],
    updatedAt: "2025-03-23 08:45:30",
    owner: "生活科技公司",
    onlineUsageCount: 2876,
    offlineUsageCount: 1432,
    likes: 876, // 添加点赞数
    github: null,
    website: "https://lifetech.com/assistant",
    logo: "/placeholder.svg?height=60&width=60&text=LA",
    type: "python",
    detail: `
# 生活助手服务器

## 概述
生活助手服务器提供各种日常生活务和便捷工具，帮助用户更高效地管理日常任务和活动。

## 核心功能
- **智能日程管理**：自动规划和优化日程安排
- **生活服务集成**：连接各种生活服务平台
- **个人助理**：提供智能化的个人理功能
- **健康追踪**：监控和分析个人健康数据

## 适用场景
- 个人日程理
- 家庭生活规划
- 健康生活方式
- 智能家居控制

## 特色服务
- 天气预报和提醒
- 交通路线规划
- 餐饮推荐和预订
- 购物清单和提醒
    `,
    tools: [
      {
        name: "weather_forecast",
        description: "获取天气预报信息",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            location: { type: "string", description: "位置" },
            days: {
              type: "number",
              description: "预报天数",
              minimum: 1,
              maximum: 7,
            },
            units: {
              type: "string",
              description: "单位制",
              enum: ["metric", "imperial"],
            },
          },
          required: ["location"],
        },
      },
      {
        name: "reminder_service",
        description: "设置提醒事项",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            title: { type: "string", description: "提醒标题" },
            time: { type: "string", description: "提醒时间" },
            repeat: {
              type: "string",
              description: "重复类型",
              enum: ["once", "daily", "weekly", "monthly"],
            },
            notification_type: {
              type: "array",
              description: "通知类型",
              items: {
                enum: ["email", "sms", "push", "app"],
              },
            },
          },
          required: ["title", "time"],
        },
      },
    ],
    callMethod: ["online"],
  },
  {
    id: 8,
    package: "wonderwhy-er/destop-commander",
    name: "知识库服务器",
    description: "人知识管理和学习资源",
    category: "knowledge",
    tags: ["education", "resources", "learning"],
    updatedAt: "2025-03-25 13:25:40",
    owner: "知识共享联盟",
    onlineUsageCount: 1987,
    offlineUsageCount: 843,
    likes: 543, // 添加点赞数
    github: "https://github.com/knowledgeshare/kbserver",
    website: "https://knowledge-alliance.org",
    logo: "/placeholder.svg?height=60&width=60&text=KB",
    type: "node",
    detail: `
# 知识库服务器

## 概述
知识库服务器是一个综合性的知识管理和学习资源平台，帮助用户组织、获取和分享知识。

## 主要特性
- **智能搜索**：基于语义的高级搜索功能
- **知识图谱**：自动构建知识之间的关联
- **个性化学习**：根据用户兴趣和学习风格推荐内容
- **协作功能**：支持团队知识共享和协作

## 适用场景
- 个人学习和研究
- 学术研究团队
- 企业知识管理
- 教育机构

## 内容类型
- 文章和论文
- 视频教程
- 交互式学习材料
- 专业课程和认证
    `,
    tools: [
      {
        name: "knowledge_search",
        description: "搜索知识库内容",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            query: { type: "string", description: "搜索关键词" },
            categories: {
              type: "array",
              description: "内容分类",
              items: { type: "string" },
            },
            sort_by: {
              type: "string",
              description: "排序方式",
              enum: ["relevance", "date", "popularity"],
            },
            max_results: {
              type: "number",
              description: "最大结果数",
              minimum: 1,
              maximum: 100,
            },
          },
          required: ["query"],
        },
      },
      {
        name: "learning_path",
        description: "生成个性化学习路径",
        points: 0, // 修改为0
        inputSchema: {
          type: "object",
          properties: {
            topic: { type: "string", description: "学习主题" },
            current_level: {
              type: "string",
              description: "当前水平",
              enum: ["beginner", "intermediate", "advanced"],
            },
            goal: { type: "string", description: "学习目标" },
            time_available: {
              type: "number",
              description: "每周可用时间(小时)",
              minimum: 1,
            },
          },
          required: ["topic", "current_level"],
        },
      },
    ],
    callMethod: ["local", "online"],
  },
]

// 翻译类别名称
export const categoryTranslations = {
  all: "全部",
  life: "生活服务",
  computer: "电脑操作",
  knowledge: "个人知识",
  business: "商业效率",
  social: "社交媒体",
  ecommerce: "电商平台",
  finance: "金融服务",
  development: "技术开发",
  other: "其他"
}

// 类别图标映射 - 使用IconPark图标
export const categoryIcons = {
  life: Home,
  computer: Computer,
  knowledge: Book,
  business: Workbench,
  social: People,
  ecommerce: ShoppingCart,
  finance: Wallet,
  development: Setting,
  other: MoreApp
}

export const statusTranslations = {
  online: "在线",
  offline: "离线",
  maintenance: "维护中",
}
