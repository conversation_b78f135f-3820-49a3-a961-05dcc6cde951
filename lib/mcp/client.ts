import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";

export class McpClient {
  private client: Client;
  private transport: StreamableHTTPClientTransport;
  private maxRetries: number = 3;
  private retryDelay: number = 1000; // 1秒

  constructor(baseUrl: string) {
    this.client = new Client({
      name: "mcp-web-client",
      version: "1.0.0"
    });
    
    // 将外部 URL 转换为本地代理 URL
    this.transport = new StreamableHTTPClientTransport(
      new URL(baseUrl)
    );
  }

  private async retryWithBackoff<T>(operation: () => Promise<T>, retries: number = this.maxRetries): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (retries > 0 && error instanceof Error && error.message.includes('CORS')) {
        console.log(`Retrying operation, ${retries} attempts remaining...`);
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        return this.retryWithBackoff(operation, retries - 1);
      }
      throw error;
    }
  }

  async connect() {
    try {
      await this.retryWithBackoff(async () => {
        await this.client.connect(this.transport);
        console.log("Connected to MCP server successfully");
      });
    } catch (error) {
      console.error("Failed to connect to MCP server:", error);
      throw error;
    }
  }

  async listPrompts() {
    return await this.retryWithBackoff(() => this.client.listPrompts());
  }

  async getPrompt(name: string, args: Record<string, any>) {
    return await this.retryWithBackoff(() => this.client.getPrompt({
      name,
      arguments: args
    }));
  }

  async listResources() {
    return await this.retryWithBackoff(() => this.client.listResources());
  }

  async listTools() {
    return await this.retryWithBackoff(() => this.client.listTools());
  }

  async readResource(uri: string) {
    return await this.retryWithBackoff(() => this.client.readResource({
      uri
    }));
  }

  async callTool(name: string, args: Record<string, any>) {
    return await this.retryWithBackoff(() => this.client.callTool({
      name,
      arguments: args
    }));
  }
} 