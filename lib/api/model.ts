import { eventStream } from "./request";
import llmApi from "./apis/llm";
export interface ModelOptions {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  tools?: Array<{
    name: string;
    description: string;
    parameters: Record<string, any>;
  }>;
  stream?: boolean;
}

export interface ModelResponse {
  content: string;
  thinking?: boolean;
  time?: number;
}

export class ModelService {
  private static instance: ModelService;
  
  private constructor() {}
  
  public static getInstance(): ModelService {
    if (!ModelService.instance) {
      ModelService.instance = new ModelService();
    }
    return ModelService.instance;
  }

  /**
   * 发送消息到模型并获取响应
   * @param options 模型调用选项
   * @param callbacks 回调函数
   */
  public async chat(options: ModelOptions, callbacks: {
    onUpdate?: (content: string, delta: string) => void;
    onFinish?: (content: string, toolCalls?: any[]) => void;
    onError?: (error: any) => void;
    onThinking?: (thinking: boolean, time?: number) => void;
  } = {}) {
    const defaultOptions = {
      stream: true,
    };

    const mergedOptions = {
      model: "deepseek-v3-250324",
      ...defaultOptions,
      ...options,
    };
    llmApi.chatStream({
      params: mergedOptions,
      stream: mergedOptions.stream,
      onUpdate: (content: string, delta: string) => {
        callbacks.onUpdate?.(content, delta);
      },
      onFinish: (content: string, toolCalls?: any[]) => {
        callbacks.onFinish?.(content, toolCalls);
      },
      onError: (error: any) => {
        callbacks.onError?.(error);
      },
      onThinking: (thinking: boolean, time?: number) => {
        callbacks.onThinking?.(thinking, time);
      }
    })
  }
}

// 导出单例实例
export const modelService = ModelService.getInstance(); 