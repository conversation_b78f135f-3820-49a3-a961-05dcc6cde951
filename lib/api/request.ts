// import { message } from "antd";
import webConfig from "./config";
import { toast } from 'sonner';
import {
  EventStreamContentType,
  fetchEventSource,
} from "@fortaine/fetch-event-source";

// 检测是否在服务端环境
const isServer = typeof window === 'undefined';

// 安全的toast函数，在服务端环境中使用console.error
const safeToast = {
  error: (message: string) => {
    if (isServer) {
      console.error(message);
    } else {
      toast.error(message);
    }
  }
};

// 创建一个事件总线来处理登录过期
export const eventBus = {
  listeners: new Set<() => void>(),
  subscribe(listener: () => void) {
    this.listeners.add(listener);
  },
  unsubscribe(listener: () => void) {
    this.listeners.delete(listener);
  },
  emit() {
    this.listeners.forEach(listener => listener());
  }
};

export let SERVICEURL = webConfig.baseRequestApi;
if (process.env.NODE_ENV == "development") {
  // SERVICEURL = "http://192.168.100.21:8888"; //香泉
  // SERVICEURL = "http://192.168.1.145:8888"; //英子
}
export const BasicUrl = "";
// import { loginFn } from '../utils/utils.ts';
interface MyObject {
  [key: string]: any;
}
let token: any;
//post get请求
export function fetchData(
  url: string,
  params: MyObject | null,
  method: string = "POST"
) {
  token = typeof window !== 'undefined' ? localStorage.getItem("mcp-token") : "";
  const abortable_promise = Promise.race([
    getTimeOutPromise(),
    getFetchPromise(url, params, method),
  ]);
  return abortable_promise;
}
//超时
function getTimeOutPromise() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      reject(new Error("network time out"));
    }, 1800000);
  });
}

//post get
function getFetchPromise(url: string, params: MyObject | null, method: string) {
  const header: MyObject = {
    "User-Agent": "",
    "Content-Type": "application/json;charset=UTF-8",
    Accept: "application/json",
  };
  token = typeof window !== 'undefined' ? localStorage.getItem("mcp-token") : "";
  if (token) header["X-Token"] = token;

  let reqUrl = "";
  if (method === "GET") {
    if (params) {
      let paramsArray: any = [];
      Object.keys(params).forEach((key) =>
        paramsArray.push(key + "=" + params[key])
      );
      if (url.search(/\?/) === -1) {
        url += "?" + paramsArray.join("&");
      } else {
        url += "&" + paramsArray.join("&");
      }
    }
    reqUrl = SERVICEURL + url;
    // if (__DEV__) {
    // console.log("请求的url==" + reqUrl);
    // console.log("params==" + JSON.stringify(params));
    // }
    return fetch(reqUrl, {
      method: method,
      headers: header,
    })
      .then(async (response) => {
        if (response.status === 401) {
          safeToast.error('登录已过期，请重新登录');
          if (!isServer) {
            eventBus.emit();
          }
          return;
        }
        if (response.ok) {
          let data: MyObject = await response.json();
          // return response.json();
          if(data.code === 0) {
            return data;
          }
        } else {
          throw new Error("network error");
        }
      })
      .then((model) => {
        return model;
      })
      .catch((error) => {
        console.log(error);
      });
  } else {
    reqUrl = SERVICEURL + url;
    return fetch(reqUrl, {
      method: method,
      body: JSON.stringify(params),
      headers: header,
    })
      .then(async (response) => {
        if (response.status === 401) {
          safeToast.error('登录已过期，请重新登录');
          if (!isServer) {
            eventBus.emit();
          }
          return;
        }
        if (response.ok) {
          let data: MyObject = await response.json();
          if (data.code === 0) {
            return data;
          } else {
            throw new Error(data.msg || '请求失败');
          }
        } else {
          throw new Error("network error");
        }
      })
      .catch((error) => {
        console.log(error);
        safeToast.error(error.message || '请求失败，请稍后重试');
        throw error;
      });
  }
}
export function fetchDownload(
  url: string,
  params: MyObject | null,
  filename: string = `${Date.now()}`
) {
  //create header
  const header: MyObject = {
    "User-Agent": "",
    "Content-Type": "application/json;charset=UTF-8",
    Accept: "application/json",
    responseType: 'blob'
  };
  token = typeof window !== 'undefined' ? localStorage.getItem("mcp-token") : "";
  if (token) header["X-Token"] = token;
  //create formData
  let formData = new FormData();
  const keys = Object.keys(params || {});
  if (params) {
    keys.forEach((key) => {
      formData.append(key, params[key]);
    });
  }
  // formData.append("file", 'xxxx');
  const reqUrl = SERVICEURL + url;
  return fetch(reqUrl, {
    method: "POST",
    body: JSON.stringify(params),
    headers: header,
  })
    .then(response => response.blob())
    .then((blob) => {
      if (blob) {
        let link: HTMLAnchorElement | null = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.setAttribute('download', `${filename}.zip`)
        link.click()
        link = null
      } else {
        throw new Error("network error");
      }
    })
    .catch((error) => {
      console.log(error);
    });
}
//上传图片的POST请求
export function fetchDataPost(url: string, params: MyObject) {
  //create header
  const header: MyObject = {
    "User-Agent": "",
    // "Content-Type": "multipart/form-data",//fetch上传不可以写Content-Type
    Accept: "application/json",
  };
  token = typeof window !== 'undefined' ? localStorage.getItem("mcp-token") : "";
  if (token) header["X-Token"] = token;
  //create formData
  let formData = new FormData();
  const keys = Object.keys(params);

  keys.forEach((key) => {
    formData.append(key, params[key]);
  });
  // formData.append("file", 'xxxx');
  const reqUrl = SERVICEURL + url;
  return fetch(reqUrl, {
    method: "POST",
    body: formData,
    headers: header,
  })
    .then(async (response) => {
      if (response.status === 401) {
        safeToast.error('登录已过期，请重新登录');
        if (!isServer) {
          eventBus.emit();
        }
        return;
      }
      if (response.ok) {
        let data: MyObject = await response.json();
        return data;
      } else {
        throw new Error("network error");
      }
    })
    .then((model) => {
      return model;
    })
    .catch((error) => {
      console.log(error);
    });
}
export async function eventStream(chatPath: string, options: any) {
  const shouldStream = options.stream;
  const controller = new AbortController();
  options.onController?.(controller);
  
  const requestTimeoutId = setTimeout(
    () => controller.abort(),
    60000
  );
  
  let think = false;
  let allMesMap: any = {
    content: "",
    time: 0,
    thinking: false,
    tool_calls: []
  }
  let timeInterval: any = null;
  let finished = false;
  
  const finish = () => {
    if (!finished) {
      try {
        let res: any = JSON.parse(allMesMap.content)
        if(res && res.code == 10010002) {
          options.onFinish(allMesMap.content, allMesMap.tool_calls);
        } else if(res.code !== 0) {
          options.onFinish(res.msg || '连接失败!', allMesMap.tool_calls);
        }
      } catch(e) {
        options.onFinish(allMesMap.content, allMesMap.tool_calls);
      }
      think = false;
      finished = true;
    }
  };
  
  controller.signal.onabort = finish;
  
  if (shouldStream) {
    let url = options.allUrl ? chatPath : SERVICEURL + chatPath;
    token = typeof window !== 'undefined' ? localStorage.getItem("mcp-token") : "";
    fetchEventSource(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        'x-token': token,
      },
      signal: controller.signal,
      body: JSON.stringify(options.params),
      async onopen(res) {
        clearTimeout(requestTimeoutId);
        if (
          !res.ok ||
          !res.headers.get("content-type")?.startsWith(EventStreamContentType) ||
          res.status !== 200
        ) {
          const responseTexts = [allMesMap.content];
          let extraInfo = await res.clone().text();
          try {
            await res.clone().json();
          } catch {}

          if (extraInfo) {
            responseTexts.push(extraInfo);
          }

          allMesMap.content = responseTexts.join("\n\n");
          return finish();
        } else {
          options.onCollBack?.();
        }
      },
      onmessage(msg) {
        if (msg.data === "[DONE]" || finished) {
          return finish();
        }
        
        try {
          let json = JSON.parse(msg.data);
          console.log(json, "jsonjsonjson")
          let delta;
          
          try {
            if(json.answer) {
              delta = json.answer;
              if (!delta) return finish();
              allMesMap.content += delta;
              // 发送完整内容
              options.onUpdate?.(allMesMap.content, delta, null, true);
            } else if (json.result) {
              delta = json.result;
              if (delta == "") return finish();
              allMesMap.content += delta;
              // 发送完整内容
              options.onUpdate?.(allMesMap.content, delta, null, true);
            } else if (json.choices) {
              // 处理工具调用
              if (json.choices[0].delta?.tool_calls) {
                options.onUpdate?.('', '', json.choices[0].delta.tool_calls, true);
                allMesMap.tool_calls = json.choices[0].delta.tool_calls;
              }
              // 处理普通消息
              else if (json.choices[0].message?.content) {
                delta = json.choices[0].message.content;
                if (delta) {
                  allMesMap.content += delta;
                  options.onUpdate?.(allMesMap.content, delta, null, true);
                }
              }
              // 处理思考状态
              else if (json.choices[0].delta?.content) {
                if(!allMesMap.thinking && json.choices[0].delta.content.startsWith('<think>')) {
                  allMesMap.thinking = true;
                  if(!timeInterval) {
                    timeInterval = setInterval(() => {
                      allMesMap.time += 1;
                    }, 1000)
                  }
                }
                
                if (json.choices[0].delta.content.startsWith('<think>')) {
                  think = true;
                } else if (json.choices[0].delta.content === '</think>') {
                  allMesMap.thinking = false;
                  clearInterval(timeInterval);
                  think = false;
                } else {
                  allMesMap.thinking = false;
                  clearInterval(timeInterval);
                  delta = json.choices[0].delta.content;
                  if (typeof delta === 'object') {
                    delta = JSON.stringify(delta);
                  }
                  allMesMap.content += delta;
                  options.onUpdate?.(allMesMap.content, delta, null, true);
                }
              }
            }
          } catch (error) {
            if (json === "[DONE]") return finish();
            delta = json;
            if (delta == '""') return finish();
            if (typeof delta === 'object') {
              delta = JSON.stringify(delta);
            }
            allMesMap.content += delta;
            // 发送完整内容
            options.onUpdate?.(allMesMap.content, delta, null, true);
          }
        } catch (e) {
          console.error("[Request] parse error", msg.data, msg);
        }
      },
      onclose() {
        finish();
      },
      onerror(e) {
        console.log(e, "error");
        options.onError?.(e);
        throw e;
      },
      openWhenHidden: true,
    });
  } else {
    const chatPayload = {
      method: "POST",
      body: JSON.stringify(options.params),
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
        token: token,
      },
    };
    const res = await fetch(SERVICEURL + chatPath, chatPayload);
    clearTimeout(requestTimeoutId);
    const resJson = await res.json();
    options.onFinish(resJson.data);
    if (resJson.code === 0 && options.onCollBack) {
      options.onCollBack();
    }
  }
}