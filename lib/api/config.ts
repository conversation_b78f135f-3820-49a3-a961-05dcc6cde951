/**
 * @description: 环境变量配置
 * @param {*} webConfig.baseRequestApi 后端请求地址
 */
interface AppConfig {
  baseRequestApi: string;
  // 添加其他需要的配置参数
}
let config: AppConfig = {
  baseRequestApi: "",
};
switch (process.env.NODE_ENV) {
  case "production":
    config = {
      baseRequestApi: "https://www.mcpcn.cc",
    };
    break;
  case "development":
    config = {
      baseRequestApi: "https://www.mcpcn.cc",
    };
    break;
  default:
    config = {
      baseRequestApi: "https://www.mcpcn.cc",
    };
}

const webConfig = config;

export default webConfig;
