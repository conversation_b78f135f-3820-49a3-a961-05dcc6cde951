import { fetchData } from "../request";
export default {
  /***
   * 获取项目列表
   * @param params
   * @returns
   * */
  getProjectsList(params: any) {
    return fetchData("/api/projects/getProjectsList", params);
  },
  //获取项目详情
  findProjectByUUID(uuid: string) {
    return fetchData(`/api/projects/findProjectByUUID/${uuid}`, null);
  },
  //点赞
  likeProject(id: number) {
    return fetchData(`/api/projects/likeProject/${id}`, null);
  },
  //取消点赞
  unlikeProject(id: number) {
    return fetchData(`/api/projects/unlikeProject/${id}`, null);
  },
  //本地安装计数
  incrementOfflineUsageCount(id: number) {
    return fetchData(`/api/projects/incrementOfflineUsageCount/${id}`, null);
  },
  //查询点赞
  getIsLiked(id: number) {
    return fetchData(`/api/projects/isLiked?projectID=${id}`, null, "GET");
  },
  

};
