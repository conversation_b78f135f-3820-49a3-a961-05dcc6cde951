import { fetchData } from "../request";
export default {
  /**
   * API密钥列表
   * @param params
   * @returns
   * */
  getApiKeys(params: any) {
    return fetchData("/api/apiKey/getApiKeys", params);
  },
  /**
   * 创建秘钥
   * @param params
   * @returns
   * */
  createApiKey(params: any) {
    return fetchData("/api/apiKey/createApiKey", params);
  },
  /**
   * 删除秘钥
   * @param params
   * @returns
   * */
  deleteApiKey(id: string) {
    return fetchData(`/api/apiKey/deleteApiKey?id=${id}`, null, 'DELETE');
  },
};