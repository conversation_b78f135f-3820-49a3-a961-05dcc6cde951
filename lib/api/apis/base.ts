import { fetchData } from "../request";
export default {
  /**
   * 手机号密码注册
   * @param params
   * @returns
   * */
  captcha() {
    return fetchData("/api/base/captcha", null);
  },
  /**
   * 登录
   * @param params
   * @returns
   * */
  unifiedLogin(params: any) {
    return fetchData("/api/base/unified-login", params);
  },
  /**
   * 手机号注册
   * @param params
   * @returns
   * */
  phoneRegister(params: any) {
    return fetchData("/api/base/phoneRegister", params);
  },
  /**
   * 发送短信验证码
   * @param params
   * @returns
   * */
  sendSMSCode(params: any) {
    return fetchData("/api/base/sendSMSCode", params);
  },
  /**
     * 微信扫码后绑定手机号
     * @param params { unionId, phone, code }
     * @returns
     */
  bindWechatPhone(params: any) {
    return fetchData("/api/base/bindWechatPhone", params);
  },
};