import { fetchData } from "../request";
export default {
  /**
   * 手机号密码注册
   * @param params
   * @returns
   * */
  registerByUsername(params: any) {
    return fetchData("/api/auth/registerByUsername", params);
  },
  /**
   * 手机号密码登录
   * @param params
   * @returns
   * */
  loginByUsername(params: any) {
    return fetchData("/api/auth/loginByUsername", params);
  },
  unifiedLogin(params: any) {
    return fetchData("/api/auth/unified-login", params);
  },
  /**
   * 发送手机验证码
   * @param params
   * @returns
   * */
  sendSmsCode(phone: string) {
    return fetchData(`/api/auth/sendSmsCode?phone=${phone}`, null);
  },
  /**
   * 手机验证码注册
   * @param params
   * @returns
   * */
  registerByPhone(params: { phone: string; password: string; code: string }) {
    return fetchData("/api/auth/registerByPhone", params);
  },
  /**
   * 手机验证码登录
   * @param params
   * @returns
   * */
  loginByPhone(params: { phone: string; smsCode: string }) {
    return fetchData("/api/auth/loginByPhone", params);
  },
  /**
   * 用户登录
   * @param params
   * @returns
   * */
  loginByUserInfo(token: string) {
    return fetchData(`/api/auth/loginByUserInfo?token=${token}`, null, 'GET');
  },
  /**
   * 修改密码
   * @param params
   * @returns
   * */
  changePassword(params: any) {
    return fetchData(`/api/auth/v3/changePassword`, params);
  },
  /**
   * 修改个人信息
   * @param params
   * @returns
   * */
  setSelfInfo(params: any) {
    return fetchData(`/api/user/setSelfInfo`, params, "PUT");
  },
  

  
};
