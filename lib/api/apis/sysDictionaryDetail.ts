import { fetchData } from "../request";
export default {
  /***
   * MCP服务器类别列表
   * */
  getDictionaryListWithProjectsCount() {
    return fetchData(`/api/sysDictionaryDetail/getDictionaryListWithProjectsCount`, null, "GET");
  },
  /***
   * MCP客户端分类列表
   * */
  GetSysDictionaryList(params: any) {
    return fetchData(`/api/sysDictionaryDetail/GetSysDictionaryList`, params, "GET");
  },
  /***
   * MCP客户端列表
   * */
  getMcpClientPublic(params: any) {
    return fetchData(`/api/mcpClient/getMcpClientPublic`, params);
  },
};
