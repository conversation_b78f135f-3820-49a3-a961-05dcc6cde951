import { fetchData } from "../request";
export default {
  /**
   * 案例列表
   * @param params
   * @returns
   * */
  getUseCasePublic(params: any) {
    return fetchData("/api/useCase/getUseCasePublic", params);
  },
  /**
   * 案例详情
   * @param params
   * @returns
   * */
  findUseCase(id: string) {
    return fetchData(`/api/useCase/findUseCase?id=${id}`, null, 'GET');
  },
  /**
   * 案例投稿
   * @param params
   * @returns
   * */
  createUseCase(params: any) {
    return fetchData(`/api/useCase/createUseCase`, params);
  }
};

