import { fetchData } from "../request";
export default {
  /**
   * 获取个人信息
   * */
  getUserInfo() {
    return fetchData(`/api/user/getUserInfo`, null, "GET");
  },
  /**
   * 修改个人信息
   * @param params
   * @returns
   * */
  setSelfInfo(params: any) {
    return fetchData(`/api/user/setSelfInfo`, params, "PUT");
  },
  /**
   * 获取个人积分
   * */
  getUserPoints() {
    return fetchData(`/api/user/getUserPoints`, null, "GET");
  },
  /**
   * 找回密码
   * */
  retrievePassword(params: any) {
    return fetchData(`/api/user/retrievePassword`, params);
  },
  /**
   * 重置密码
   * */
  changePassword(params: any) {
    return fetchData(`/api/user/changePassword`, params);
  },

};
