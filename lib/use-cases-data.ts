export type UseCase = {
  CreatedAt: string
  client: any
  servers: any
  id: number
  author: string
  avatar: string
  homepage: string
  content: string
  summary: string
  video: string | null
  cover: string | null
  likes: number
  comments: number
  shares: number
  date: string
  serverIds: number[] // Changed from serverId to serverIds array
  clientId: number | null
}

export const useCasesData: UseCase[] = [
  // {
  //   id: 1,
  //   author: "<PERSON><PERSON><PERSON>",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/sidahuja",
  //   content:
  //     '🌱 Built an MCP that lets <PERSON> talk directly to <PERSON><PERSON>der. It helps you create beautiful 3D scenes using just prompts! Here\'s a demo of me creating a "low-poly dragon guarding treasure" scene in just a few sentences🌟',
  //   summary: "使用MCP让Claude直接与Blender通信，通过简单的提示词创建精美的3D场景。",
  //   video: "https://www.youtube.com/embed/dQw4w9WgXcQ",
  //   cover: null,
  //   likes: 1245,
  //   comments: 87,
  //   shares: 324,
  //   date: "2025-03-25",
  //   serverIds: [4], // 媒体流服务器
  //   clientId: 3, // MCP Visualizer
  // },
  // {
  //   id: 2,
  //   author: "Alvaro Cintas",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/alvarocintas",
  //   content:
  //     "You can now clone any website just by writing a prompt. Simply add the new Firecrawl MCP server to your favorite AI coding tool for improved web data extraction, and let Claude code it for you. Here's how:",
  //   summary: "通过Firecrawl MCP服务器，只需编写提示词即可克隆任何网站，大大提升了网页数据提取能力。",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 2356,
  //   comments: 142,
  //   shares: 578,
  //   date: "2025-03-24",
  //   serverIds: [3, 2], // 网站托管服务器 + 数据处理服务器
  //   clientId: 1, // MCP Studio
  // },
  // {
  //   id: 3,
  //   author: "Eric Listin",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://twitter.com/EricListin",
  //   content:
  //     "I've just built custom MCP server for Cursor. And launched it locally. This MCP makes Cursor play sound notification after each task Now you can scroll X while waiting and never miss when it's done. Let's go 👇",
  //   summary:
  //     "为Cursor构建的自定义MCP服务器，每完成一项任务就会播放声音通知，让你在等待时可以浏览其他内容而不会错过完成提示。",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 1876,
  //   comments: 95,
  //   shares: 412,
  //   date: "2025-03-23",
  //   serverIds: [6], // 开发测试服务器
  //   clientId: 4, // MCP Commander
  // },
  // {
  //   id: 4,
  //   author: "Perplexity",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://www.perplexity.ai",
  //   content:
  //     "The Perplexity API Model Context Protocol (MCP) is now available. We've built an MCP server for Sonar, giving AI assistants real-time, web search research capabilities. Powered by Perplexity, Claude can now search the web and deliver real-time and accurate insights on demand.",
  //   summary:
  //     "Perplexity API的MCP现已可用，为Sonar构建的MCP服务器使AI助手具备实时网络搜索研究能力，Claude现在可以搜索网络并按需提供实时准确的见解。",
  //   video: "https://www.youtube.com/embed/dQw4w9WgXcQ",
  //   cover: null,
  //   likes: 3421,
  //   comments: 187,
  //   shares: 892,
  //   date: "2025-03-22",
  //   serverIds: [8, 2], // 知识库服务器 + 数据处理服务器
  //   clientId: 5, // MCP Explorer
  // },
  // {
  //   id: 5,
  //   author: "Mushtaq Bilal, PhD",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://twitter.com/MushtaqBilalPhD",
  //   content:
  //     "MCP (Model Context Protocol) is the next big thing in AI. It lets you integrate academic databases with LLMs. But most academics don't know about it. Here's how to connect Claude with PubMed using MCP (in less than 15 min):",
  //   summary:
  //     "MCP是AI领域的下一个重大突破，它允许将学术数据库与大型语言模型集成。这里介绍如何在15分钟内使用MCP将Claude与PubMed连接起来。",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 1543,
  //   comments: 112,
  //   shares: 478,
  //   date: "2025-03-21",
  //   serverIds: [2, 8], // 数据处理服务器 + 知识库服务器
  //   clientId: 2, // MCP Connect
  // },
  // {
  //   id: 6,
  //   author: "Weaviate • vector search",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://weaviate.io",
  //   content:
  //     "🔍 Introducing the Weaviate MCP Server! You can now connect your MCP host to Weaviate's vector search capabilities! The Model Context Protocol (MCP) introduced by @AnthropicAI is a proposal to standardize AI interfaces. AI models need context from external knowledge sources to",
  //   summary:
  //     "推出Weaviate MCP服务器！现在可以将MCP主机连接到Weaviate的向量搜索功能，使AI模型能够从外部知识源获取上下文信息。",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 1876,
  //   comments: 98,
  //   shares: 523,
  //   date: "2025-03-20",
  //   serverIds: [5], // 数据库服务器集群
  //   clientId: 6, // MCP Sync
  // },
  // {
  //   id: 7,
  //   author: "Alex Chen",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/alexgamedev",
  //   content:
  //     "🎮 Just integrated our game with the Alpha MCP server! Now players can use AI to generate custom quests and NPCs in real-time. The latency is incredibly low - perfect for competitive gameplay. Check out this demo of a procedurally generated dungeon with unique enemies:",
  //   summary: "将游戏与Alpha MCP服务器集成，玩家现在可以使用AI实时生成自定义任务和NPC。延迟极低，非常适合竞技游戏。",
  //   video: "https://www.youtube.com/embed/dQw4w9WgXcQ",
  //   cover: null,
  //   likes: 3245,
  //   comments: 156,
  //   shares: 789,
  //   date: "2025-03-19",
  //   serverIds: [1, 4], // 游戏服务器 Alpha + 媒体流服务器
  //   clientId: 1, // MCP Studio
  // },
  // {
  //   id: 8,
  //   author: "Sarah Johnson",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/sarahj_dev",
  //   content:
  //     "Built a personal assistant that manages my entire home using the Life Assistant MCP server. It controls my smart home devices, orders groceries when I'm running low, and even suggests recipes based on what's in my fridge. Game changer for busy professionals!",
  //   summary:
  //     "使用Life Assistant MCP服务器构建了一个管理整个家庭的个人助手，控制智能家居设备，在食物不足时订购杂货，甚至根据冰箱中的食材推荐食谱。",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 2789,
  //   comments: 134,
  //   shares: 567,
  //   date: "2025-03-18",
  //   serverIds: [7], // 生活助手服务器
  //   clientId: 2, // MCP Connect
  // },
  // {
  //   id: 9,
  //   author: "David Wong",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/davidwong",
  //   content:
  //     "Our esports team is using the Alpha Game Server MCP to analyze player performance in real-time. The AI provides instant feedback during matches and suggests tactical adjustments. We've seen a 30% improvement in win rates since implementation! Here's how we set it up:",
  //   summary:
  //     "我们的电竞团队使用Alpha Game Server MCP实时分析玩家表现，AI在比赛中提供即时反���并建议战术调整，实施后胜率提高了30%。",
  //   video: "https://www.youtube.com/embed/dQw4w9WgXcQ",
  //   cover: null,
  //   likes: 4532,
  //   comments: 213,
  //   shares: 876,
  //   date: "2025-03-17",
  //   serverIds: [1, 2], // 游戏服务器 Alpha + 数据处理服务器
  //   clientId: 8, // MCP Dashboard
  // },
  // {
  //   id: 10,
  //   author: "Maria Rodriguez",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/maria_ai",
  //   content:
  //     "Just finished building an AI-powered financial advisor using the Database Server Cluster MCP. It analyzes market trends, portfolio performance, and risk factors in milliseconds. The high availability design means zero downtime for our clients. Demo video below:",
  //   summary:
  //     "使用Database Server Cluster MCP构建了一个AI驱动的财务顾问，可以在毫秒内分析市场趋势、投资组合表现和风险因素，高可用性设计确保客户零停机时间。",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 1987,
  //   comments: 145,
  //   shares: 432,
  //   date: "2025-03-16",
  //   serverIds: [5, 2], // 数据库服务器集群 + 数据处理服务器
  //   clientId: 7, // MCP Security Scanner
  // },
  // {
  //   id: 11,
  //   author: "Raj Patel",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/raj_healthtech",
  //   content:
  //     "Our healthcare startup just deployed the Knowledge Base MCP server to help doctors access the latest medical research instantly. Claude can now search through millions of papers and clinical trials to provide evidence-based recommendations. Reducing research time from hours to seconds!",
  //   summary:
  //     "我们的医疗创业公司部署了Knowledge Base MCP服务器，帮助医生即时获取最新医学研究。Claude现在可以搜索数百万篇论文和临床试验，提供循证医学建议，将研究时间从数小时缩短到几秒钟。",
  //   video: "https://www.youtube.com/embed/dQw4w9WgXcQ",
  //   cover: null,
  //   likes: 3567,
  //   comments: 189,
  //   shares: 912,
  //   date: "2025-03-15",
  //   serverIds: [8], // 知识库服务器
  //   clientId: 3, // MCP Visualizer
  // },
  // {
  //   id: 12,
  //   author: "Emma Wilson",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/emma_ux",
  //   content:
  //     "The Life Assistant MCP server has transformed my morning routine! I connected it to my calendar, weather app, and smart home devices. Now Claude wakes me up, briefs me on my schedule, adjusts the temperature, and starts my coffee maker - all based on my preferences and the day ahead.",
  //   summary:
  //     "Life Assistant MCP服务器彻底改变了我的晨间例行程序！将其连接到日历、天气应用和智能家居设备后，Claude现在会叫醒我，简要介绍我的日程安排，调整温度，并启动咖啡机——所有这些都基于我的偏好和当天的安排。",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 2345,
  //   comments: 167,
  //   shares: 543,
  //   date: "2025-03-14",
  //   serverIds: [7], // 生活助手服务器
  //   clientId: 5, // MCP Explorer
  // },
  // {
  //   id: 13,
  //   author: "Michael Zhang",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/mzhang_dev",
  //   content:
  //     "Just deployed our e-commerce platform on the Website Hosting MCP server. The automatic scaling handled our flash sale traffic perfectly - 200k concurrent users with zero downtime! The integrated CDN made page loads lightning fast even with heavy product images.",
  //   summary:
  //     "在Website Hosting MCP服务器上部署了我们的电子商务平台。自动扩展完美处理了我们的限时抢购流量——20万并发用户，零停机时间！集成的CDN使页面加载速度极快，即使是大量产品图片也不例外。",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 1876,
  //   comments: 124,
  //   shares: 398,
  //   date: "2025-03-13",
  //   serverIds: [3], // 网站托管服务器
  //   clientId: 8, // MCP Dashboard
  // },
  // {
  //   id: 14,
  //   author: "Sophia Chen",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/sophia_data",
  //   content:
  //     "Our data science team connected Claude to our customer database using the Data Processing MCP server. Now our marketing team can ask natural language questions about customer segments and get instant visualizations. No SQL knowledge required! Here's how we implemented it:",
  //   summary:
  //     "我们的数据科学团队使用Data Processing MCP服务器将Claude连接到客户数据库。现在，我们的营销团队可以用自然语言提问客户细分相关问题，并获得即时可视化结果，无需SQL知识！",
  //   video: "https://www.youtube.com/embed/dQw4w9WgXcQ",
  //   cover: null,
  //   likes: 2987,
  //   comments: 178,
  //   shares: 645,
  //   date: "2025-03-12",
  //   serverIds: [2, 5], // 数据处理服务器 + 数据库服务器集群
  //   clientId: 3, // MCP Visualizer
  // },
  // {
  //   id: 15,
  //   author: "James Wilson",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/jwilson_media",
  //   content:
  //     "Our streaming platform just integrated the Media Stream MCP server and the results are incredible! AI-powered real-time video enhancement, automatic content moderation, and personalized stream recommendations. Viewer engagement is up 45% in just two weeks!",
  //   summary:
  //     "我们的流媒体平台刚刚集成了Media Stream MCP服务器，结果令人难以置信！AI驱动的实时视频增强、自动内容审核和个性化流推荐。仅仅两周内，观众参与度就提高了45%！",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 3654,
  //   comments: 198,
  //   shares: 876,
  //   date: "2025-03-11",
  //   serverIds: [4], // 媒体流服务器
  //   clientId: 8, // MCP Dashboard
  // },
  // {
  //   id: 16,
  //   author: "Olivia Park",
  //   avatar: "/placeholder.svg?height=80&width=80",
  //   homepage: "https://github.com/olivia_dev",
  //   content:
  //     "Just set up the Development Test MCP server for our QA team. Now Claude can automatically generate test cases, run regression tests, and report bugs with detailed reproduction steps. Cut our testing time in half and improved coverage by 35%!",
  //   summary:
  //     "为我们的QA团队设置了Development Test MCP服务器。现在Claude可以自动生成测试用例，运行回归测试，并报告带有详细复现步骤的bug。将我们的测试时间减半，并将覆盖率提高了35%！",
  //   video: null,
  //   cover: "/placeholder.svg?height=400&width=600",
  //   likes: 1765,
  //   comments: 132,
  //   shares: 421,
  //   date: "2025-03-10",
  //   serverIds: [6], // 开发测试服务器
  //   clientId: 4, // MCP Commander
  // },
]
