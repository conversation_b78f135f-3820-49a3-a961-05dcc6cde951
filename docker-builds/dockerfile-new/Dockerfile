FROM --platform=$BUILDPLATFORM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/node:20-alpine3.20

WORKDIR /opt/mcpcn-web

# 安装必要的系统依赖
RUN apk add --no-cache libc6-compat

# 设置 npm 镜像源并安装 PM2
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pm2 && \
    npm cache clean --force

COPY package*.json ./
COPY yarn.lock ./

# 安装所有依赖（包括开发依赖）
RUN npm install

# 复制所有源文件
COPY . .

# 创建日志目录
RUN mkdir -p /opt/mcpcn-web/logs

# 创建非 root 用户
RUN chmod +x /opt/mcpcn-web/docker-builds/dockerfile-new/start.sh

EXPOSE 80
ENV PORT=80 HOSTNAME="0.0.0.0"

CMD ["sh", "/opt/mcpcn-web/docker-builds/dockerfile-new/start.sh"]
