version: '3.8'

services:
  mcpcn-web:
    build:
      context: ../../
      dockerfile: docker-builds/dockerfile-new/Dockerfile
    image: mcpcn-web-new:latest
    container_name: mcpcn-web-new
    restart: unless-stopped
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
      - PORT=80
    volumes:
      # 挂载日志目录到宿主机，方便查看日志
      - ../../logs:/opt/mcpcn-web/logs
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

# 如果需要数据持久化，可以添加 volumes
volumes:
  app_data:
    driver: local
