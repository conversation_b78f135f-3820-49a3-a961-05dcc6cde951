#!/bin/bash

# 部署脚本 - Dockerfile.new 方式
# 使用方法:
#   ./deploy.sh                    # 使用时间戳作为版本号
#   ./deploy.sh v1.0.0            # 使用自定义版本号
#   ./deploy.sh 20241201-release  # 使用自定义版本号
set -e

echo "🚀 开始部署 MCPCN Web 应用 (Dockerfile.new 方式)..."

# 确保在正确的目录
cd "$(dirname "$0")"

# 创建日志目录
mkdir -p ../../logs

# 构建 Docker 镜像
echo "📦 构建 Docker 镜像..."
docker build -f Dockerfile -t mcpcn-web-new:latest ../../

# 推送到阿里云镜像仓库
echo "📤 推送镜像到阿里云仓库..."
REGISTRY_URL="registry.cn-hangzhou.aliyuncs.com/hello_test1/mcpcn-web"
VERSION=${1:-$(date +%Y%m%d-%H%M%S)}  # 支持命令行参数指定版本号

echo "🏷️ 使用版本号: ${VERSION}"

# 登录阿里云镜像仓库
echo "🔐 登录阿里云镜像仓库..."

# 阿里云镜像仓库登录信息
DOCKER_USERNAME="fengjc超"
DOCKER_PASSWORD="chao1116"
DOCKER_REGISTRY="registry.cn-hangzhou.aliyuncs.com"

# 使用密码自动登录
echo "🔑 正在登录..."
echo "$DOCKER_PASSWORD" | docker login --username="$DOCKER_USERNAME" --password-stdin "$DOCKER_REGISTRY"

if [ $? -eq 0 ]; then
    echo "✅ 登录成功！"
else
    echo "❌ 登录失败！请检查用户名和密码是否正确。"
    exit 1
fi

# 标记镜像
echo "🏷️ 标记镜像..."
docker tag mcpcn-web-new:latest ${REGISTRY_URL}:${VERSION}

# 推送镜像
echo "⬆️ 推送镜像..."
docker push ${REGISTRY_URL}:${VERSION}

echo "✅ 镜像推送完成！"
echo "📦 镜像地址: ${REGISTRY_URL}:${VERSION}"

# 清理本地镜像以节省磁盘空间
echo "🧹 清理本地镜像..."
docker rmi -f mcpcn-web-new:latest ${REGISTRY_URL}:${VERSION} || true

# 清理悬空镜像
echo "🗑️ 清理悬空镜像..."
docker image prune -f

echo "💾 磁盘空间清理完成！"




