"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import authApi from "@/lib/api/apis/auth"
import userApi from "@/lib/api/apis/user"
import { eventBus } from "@/lib/api/request"
// 修改 User 类型，添加积分字段
type User = {
  ID: string
  name: string
  email: string
  headerImg: string
  nickName: string
  apiKey?: string
  token: string
  points: number // 积分字段
  freePoints: number // 免费赠送积分字段
  havePassword: boolean
}

type AuthContextType = {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  login: (data: User) => void
  logout: () => void
  isLoading: boolean
  setUser: (user: User | null) => void
  setUserInfo: (data: User) => void
  loginDialogOpen: boolean
  setLoginDialogOpen: (open: boolean) => void
}

const AUTH_STORAGE_KEY_TOKEN = "mcp-token"

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [loginDialogOpen, setLoginDialogOpen] = useState(false)

  // 订阅登录过期事件
  useEffect(() => {
    const handleLogout = () => {
      logout();
    };
    
    eventBus.subscribe(handleLogout);
    
    return () => {
      eventBus.unsubscribe(handleLogout);
    };
  }, []);

  // 初始化时从localStorage加载用户信息
  useEffect(() => {
    const loadUserFromStorage = () => {
      try {
        const storedToken = localStorage.getItem(AUTH_STORAGE_KEY_TOKEN)
        if (storedToken) {
          setToken(storedToken)
          getUserInfo()
        }
      } catch (error) {
        console.error("Failed to load user from localStorage:", error)
      } finally {
        setIsLoading(false)
      }
    }

    // 在客户端环境中执行
    if (typeof window !== "undefined") {
      loadUserFromStorage()
    } else {
      setIsLoading(false)
    }
  }, [])
  const getUserInfo = async () => {
    let response: any = await userApi.getUserInfo()
    if(response.code === 0){
      setUser(response.data.userInfo)
    }
  }
  const setUserInfo = (data: User) => {
    setUser(data)
    setToken(data.token)
    if (typeof window !== "undefined") {
      localStorage.setItem(AUTH_STORAGE_KEY_TOKEN, data.token)
    }
  }
  const login = (data: User) => {
    setUserInfo(data)
    if (typeof window !== "undefined") {
      location.reload();
    }
  }

  const logout = () => {
    setUser(null)
    if (typeof window !== "undefined") {
      localStorage.removeItem(AUTH_STORAGE_KEY_TOKEN)
      // 清除cookies中的x-token
      document.cookie = "x-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"
      location.reload();
    }
  }

  return (
    <AuthContext.Provider value={{ user, token, isAuthenticated: !!user, login, setUserInfo, logout, isLoading, setUser, loginDialogOpen, setLoginDialogOpen }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
function setUser(arg0: null) {
  throw new Error("Function not implemented.")
}

function setToken(token: any) {
  throw new Error("Function not implemented.")
}

